// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.di;

import com.example.abonekaptanmobile.data.local.AppDatabase;
import com.example.abonekaptanmobile.data.local.dao.CommunityPatternDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideCommunityPatternDaoFactory implements Factory<CommunityPatternDao> {
  private final Provider<AppDatabase> appDatabaseProvider;

  public AppModule_ProvideCommunityPatternDaoFactory(Provider<AppDatabase> appDatabaseProvider) {
    this.appDatabaseProvider = appDatabaseProvider;
  }

  @Override
  public CommunityPatternDao get() {
    return provideCommunityPatternDao(appDatabaseProvider.get());
  }

  public static AppModule_ProvideCommunityPatternDaoFactory create(
      Provider<AppDatabase> appDatabaseProvider) {
    return new AppModule_ProvideCommunityPatternDaoFactory(appDatabaseProvider);
  }

  public static CommunityPatternDao provideCommunityPatternDao(AppDatabase appDatabase) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideCommunityPatternDao(appDatabase));
  }
}
