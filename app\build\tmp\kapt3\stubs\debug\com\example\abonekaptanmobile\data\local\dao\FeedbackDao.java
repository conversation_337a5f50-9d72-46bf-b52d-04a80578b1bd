package com.example.abonekaptanmobile.data.local.dao;

import androidx.room.*;
import com.example.abonekaptanmobile.data.local.entity.FeedbackEntity;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\t\n\u0002\b\u0002\bg\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\b\u001a\u00020\u00032\u0006\u0010\t\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0016\u0010\u000b\u001a\u00020\u00032\u0006\u0010\f\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u000e\u00a8\u0006\u000f"}, d2 = {"Lcom/example/abonekaptanmobile/data/local/dao/FeedbackDao;", "", "clearAllFeedback", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPendingFeedback", "", "Lcom/example/abonekaptanmobile/data/local/entity/FeedbackEntity;", "insertFeedback", "feedback", "(Lcom/example/abonekaptanmobile/data/local/entity/FeedbackEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "markFeedbackAsProcessed", "feedbackId", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
@androidx.room.Dao()
public abstract interface FeedbackDao {
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertFeedback(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.local.entity.FeedbackEntity feedback, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM feedback WHERE processed = 0 ORDER BY createdAt ASC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPendingFeedback(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.abonekaptanmobile.data.local.entity.FeedbackEntity>> $completion);
    
    @androidx.room.Query(value = "UPDATE feedback SET processed = 1 WHERE id = :feedbackId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object markFeedbackAsProcessed(long feedbackId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM feedback")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object clearAllFeedback(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}