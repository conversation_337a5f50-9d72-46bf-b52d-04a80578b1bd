package com.example.abonekaptanmobile.data.local.dao;

import androidx.room.Dao;
import androidx.room.Query;
import androidx.room.Upsert;
import com.example.abonekaptanmobile.data.local.entity.SubscriptionPatternEntity;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0003\bg\u0018\u00002\u00020\u0001J\u001e\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\u0007J\u0018\u0010\b\u001a\u0004\u0018\u00010\u00042\u0006\u0010\t\u001a\u00020\nH\u00a7@\u00a2\u0006\u0002\u0010\u000bJ\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00a7@\u00a2\u0006\u0002\u0010\rJ\u0016\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0004H\u00a7@\u00a2\u0006\u0002\u0010\u0011\u00a8\u0006\u0012"}, d2 = {"Lcom/example/abonekaptanmobile/data/local/dao/CommunityPatternDao;", "", "getNonSubscriptionPatterns", "", "Lcom/example/abonekaptanmobile/data/local/entity/SubscriptionPatternEntity;", "minRejectionVotes", "", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPatternByServiceName", "serviceName", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getReliableSubscriptionPatterns", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "upsertPattern", "", "pattern", "(Lcom/example/abonekaptanmobile/data/local/entity/SubscriptionPatternEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
@androidx.room.Dao()
public abstract interface CommunityPatternDao {
    
    /**
     * Turkish: Güvenilir ve abonelik olarak işaretlenmiş kalıpları getirir.
     * English: Fetches reliable patterns marked as subscriptions.
     * Test amacıyla basitleştirilmiş sorgu.
     */
    @androidx.room.Query(value = "SELECT * FROM subscription_patterns WHERE isSubscription = 1 AND source = \'default_verified\' ORDER BY priority DESC, approvedCount DESC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getReliableSubscriptionPatterns(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.abonekaptanmobile.data.local.entity.SubscriptionPatternEntity>> $completion);
    
    /**
     * Turkish: Topluluk tarafından "abonelik değil" olarak işaretlenmiş veya yüksek oranda reddedilmiş kalıpları getirir.
     * English: Fetches patterns marked as "not a subscription" by the community or highly rejected ones.
     */
    @androidx.room.Query(value = "SELECT * FROM subscription_patterns WHERE isSubscription = 0 AND (source = \'community_rejected\' OR (rejectedCount > :minRejectionVotes AND approvedCount < rejectedCount)) ORDER BY rejectedCount DESC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getNonSubscriptionPatterns(int minRejectionVotes, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.abonekaptanmobile.data.local.entity.SubscriptionPatternEntity>> $completion);
    
    /**
     * Turkish: Belirli bir servis adına göre kalıbı getirir.
     * English: Fetches a pattern by its service name.
     */
    @androidx.room.Query(value = "SELECT * FROM subscription_patterns WHERE serviceName = :serviceName COLLATE NOCASE LIMIT 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPatternByServiceName(@org.jetbrains.annotations.NotNull()
    java.lang.String serviceName, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.abonekaptanmobile.data.local.entity.SubscriptionPatternEntity> $completion);
    
    /**
     * Turkish: Bir kalıbı ekler veya günceller.
     * English: Inserts or updates a pattern.
     */
    @androidx.room.Upsert()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object upsertPattern(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.local.entity.SubscriptionPatternEntity pattern, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}