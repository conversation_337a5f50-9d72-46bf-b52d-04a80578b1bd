,com/example/abonekaptanmobile/AboneKaptanApp*com/example/abonekaptanmobile/MainActivity4com/example/abonekaptanmobile/auth/GoogleAuthManager4com/example/abonekaptanmobile/data/local/AppDatabase>com/example/abonekaptanmobile/data/local/AppDatabase$Companion@com/example/abonekaptanmobile/data/local/dao/CommunityPatternDao8com/example/abonekaptanmobile/data/local/dao/FeedbackDao>com/example/abonekaptanmobile/data/local/entity/FeedbackEntityIcom/example/abonekaptanmobile/data/local/entity/SubscriptionPatternEntity;com/example/abonekaptanmobile/data/local/entity/PatternType8com/example/abonekaptanmobile/data/remote/HuggingFaceApi2com/example/abonekaptanmobile/data/remote/GmailApiHcom/example/abonekaptanmobile/data/remote/model/GmailMessageListResponse9com/example/abonekaptanmobile/data/remote/model/MessageId<com/example/abonekaptanmobile/data/remote/model/GmailMessage>com/example/abonekaptanmobile/data/remote/model/MessagePayload=com/example/abonekaptanmobile/data/remote/model/MessageHeader?com/example/abonekaptanmobile/data/remote/model/MessagePartBodyBcom/example/abonekaptanmobile/data/remote/model/HuggingFaceRequestEcom/example/abonekaptanmobile/data/remote/model/HuggingFaceParametersCcom/example/abonekaptanmobile/data/remote/model/HuggingFaceResponseDcom/example/abonekaptanmobile/data/remote/model/ClassificationResultNcom/example/abonekaptanmobile/data/remote/model/ClassificationResult$CompanionLcom/example/abonekaptanmobile/data/remote/model/DetailedClassificationResultFcom/example/abonekaptanmobile/data/remote/model/HybridValidationResultHcom/example/abonekaptanmobile/data/repository/CommunityPatternRepository@com/example/abonekaptanmobile/data/repository/FeedbackRepository=com/example/abonekaptanmobile/data/repository/GmailRepositoryCcom/example/abonekaptanmobile/data/repository/HuggingFaceRepositoryMcom/example/abonekaptanmobile/data/repository/HuggingFaceRepository$Companion*com/example/abonekaptanmobile/di/AppModule4com/example/abonekaptanmobile/model/CancellationInfo3com/example/abonekaptanmobile/model/ClassifiedEmail4com/example/abonekaptanmobile/model/SubscriptionType-com/example/abonekaptanmobile/model/EmailType,com/example/abonekaptanmobile/model/RawEmail4com/example/abonekaptanmobile/model/SubscriptionItem6com/example/abonekaptanmobile/model/SubscriptionStatus=com/example/abonekaptanmobile/services/SubscriptionClassifierTcom/example/abonekaptanmobile/services/SubscriptionClassifier$AIClassificationResult?com/example/abonekaptanmobile/services/SubscriptionClassifierKt9com/example/abonekaptanmobile/ui/screens/FeedbackDialogKt8com/example/abonekaptanmobile/ui/screens/LabTestDialogKt7com/example/abonekaptanmobile/ui/screens/SignInScreenKtAcom/example/abonekaptanmobile/ui/screens/SubscriptionListScreenKt.com/example/abonekaptanmobile/ui/theme/ColorKt.com/example/abonekaptanmobile/ui/theme/ThemeKt-com/example/abonekaptanmobile/ui/theme/TypeKt8com/example/abonekaptanmobile/ui/viewmodel/MainViewModel.com/example/abonekaptanmobile/utils/TestResult8com/example/abonekaptanmobile/utils/HybridApproachTesterBcom/example/abonekaptanmobile/utils/HybridApproachTester$Companion;com/example/abonekaptanmobile/workers/ProcessFeedbackWorkerEcom/example/abonekaptanmobile/workers/ProcessFeedbackWorker$Companion.kotlin_module                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       