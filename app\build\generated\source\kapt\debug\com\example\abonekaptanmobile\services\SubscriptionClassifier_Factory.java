// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.services;

import com.example.abonekaptanmobile.data.repository.CommunityPatternRepository;
import com.example.abonekaptanmobile.data.repository.HuggingFaceRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SubscriptionClassifier_Factory implements Factory<SubscriptionClassifier> {
  private final Provider<CommunityPatternRepository> communityPatternRepoProvider;

  private final Provider<HuggingFaceRepository> huggingFaceRepositoryProvider;

  public SubscriptionClassifier_Factory(
      Provider<CommunityPatternRepository> communityPatternRepoProvider,
      Provider<HuggingFaceRepository> huggingFaceRepositoryProvider) {
    this.communityPatternRepoProvider = communityPatternRepoProvider;
    this.huggingFaceRepositoryProvider = huggingFaceRepositoryProvider;
  }

  @Override
  public SubscriptionClassifier get() {
    return newInstance(communityPatternRepoProvider.get(), huggingFaceRepositoryProvider.get());
  }

  public static SubscriptionClassifier_Factory create(
      Provider<CommunityPatternRepository> communityPatternRepoProvider,
      Provider<HuggingFaceRepository> huggingFaceRepositoryProvider) {
    return new SubscriptionClassifier_Factory(communityPatternRepoProvider, huggingFaceRepositoryProvider);
  }

  public static SubscriptionClassifier newInstance(CommunityPatternRepository communityPatternRepo,
      HuggingFaceRepository huggingFaceRepository) {
    return new SubscriptionClassifier(communityPatternRepo, huggingFaceRepository);
  }
}
