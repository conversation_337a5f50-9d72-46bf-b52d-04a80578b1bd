package com.example.abonekaptanmobile.services;

import android.util.Log;
import com.example.abonekaptanmobile.data.local.entity.PatternType;
import com.example.abonekaptanmobile.data.local.entity.SubscriptionPatternEntity;
import com.example.abonekaptanmobile.data.remote.model.ClassificationResult;
import com.example.abonekaptanmobile.data.repository.CommunityPatternRepository;
import com.example.abonekaptanmobile.data.repository.HuggingFaceRepository;
import com.example.abonekaptanmobile.data.remote.model.TwoStageAnalysisResult;
import com.example.abonekaptanmobile.data.remote.model.FinalSubscriptionStatus;
import com.example.abonekaptanmobile.model.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.PatternSyntaxException;
import javax.inject.Inject;

/**
 * Turkish: E-postaları abonelik durumuna göre sınıflandıran servis.
 * English: Service that classifies emails based on subscription status.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00b6\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\"\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010%\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0007\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010$\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\u000b\n\u0002\b\n\u0018\u00002\u00020\u0001:\u0001^B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006JD\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00160\u00152\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00160\b2\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00190\b2\u0018\u0010\u001a\u001a\u0014\u0012\u0004\u0012\u00020\u000f\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001c0\u00150\u001bH\u0002J^\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u001e0\b2\f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00160\b2:\b\u0002\u0010 \u001a4\u0012\u0013\u0012\u00110\"\u00a2\u0006\f\b#\u0012\b\b$\u0012\u0004\b\b(%\u0012\u0013\u0012\u00110\u000f\u00a2\u0006\f\b#\u0012\b\b$\u0012\u0004\b\b(&\u0012\u0004\u0012\u00020\'\u0018\u00010!H\u0086@\u00a2\u0006\u0002\u0010(J^\u0010)\u001a\b\u0012\u0004\u0012\u00020\u001e0\b2\f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00160\b2:\b\u0002\u0010 \u001a4\u0012\u0013\u0012\u00110\"\u00a2\u0006\f\b#\u0012\b\b$\u0012\u0004\b\b(%\u0012\u0013\u0012\u00110\u000f\u00a2\u0006\f\b#\u0012\b\b$\u0012\u0004\b\b(&\u0012\u0004\u0012\u00020\'\u0018\u00010!H\u0086@\u00a2\u0006\u0002\u0010(J\u0010\u0010*\u001a\u00020+2\u0006\u0010,\u001a\u00020-H\u0002J\u0010\u0010.\u001a\u00020/2\u0006\u00100\u001a\u00020\u000fH\u0002J\u0010\u00101\u001a\u0002022\u0006\u00103\u001a\u00020\u000fH\u0002J\u0010\u00104\u001a\u0002052\u0006\u00103\u001a\u00020\u000fH\u0002J\u0018\u00106\u001a\u00020+2\u0006\u00107\u001a\u0002052\u0006\u00108\u001a\u000202H\u0002J\u0010\u00109\u001a\u0002052\u0006\u0010:\u001a\u00020\u000fH\u0002J\u0018\u0010;\u001a\u0002022\u0006\u00103\u001a\u00020\u000f2\u0006\u0010<\u001a\u00020\u000fH\u0002J\u0018\u0010=\u001a\u00020+2\u0006\u00107\u001a\u0002052\u0006\u00108\u001a\u000202H\u0002J\u001e\u0010>\u001a\u00020\u001e2\u0006\u0010&\u001a\u00020?2\f\u0010@\u001a\b\u0012\u0004\u0012\u00020\u00160\bH\u0002J.\u0010A\u001a\b\u0012\u0004\u0012\u00020\u001e0\b2\u0018\u0010B\u001a\u0014\u0012\u0004\u0012\u00020\u000f\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001c0\b0CH\u0082@\u00a2\u0006\u0002\u0010DJ\u0018\u0010E\u001a\u00020/2\u0006\u00108\u001a\u0002022\u0006\u0010F\u001a\u00020\u0016H\u0002J\u0010\u0010G\u001a\u00020/2\u0006\u0010F\u001a\u00020\u0016H\u0002J\u001e\u0010H\u001a\u00020\u000f2\u0006\u0010F\u001a\u00020\u00162\f\u0010I\u001a\b\u0012\u0004\u0012\u00020\u00190\bH\u0002J\u0018\u0010J\u001a\u00020K2\u0006\u0010L\u001a\u0002052\u0006\u0010M\u001a\u000205H\u0002J\u0012\u0010N\u001a\u0004\u0018\u00010\u000f2\u0006\u0010O\u001a\u00020\u000fH\u0002J\"\u0010P\u001a\u00020\u000f2\u0006\u0010Q\u001a\u00020\u000f2\u0006\u0010R\u001a\u00020\u000f2\b\u0010S\u001a\u0004\u0018\u00010\u000fH\u0002J\u0010\u0010T\u001a\u00020U2\u0006\u0010Q\u001a\u00020\u000fH\u0002J\u0010\u0010V\u001a\u00020U2\u0006\u0010:\u001a\u00020\u000fH\u0002J\u0018\u0010W\u001a\u00020U2\u0006\u0010F\u001a\u00020\u00162\u0006\u0010X\u001a\u00020\u0019H\u0002J\u0010\u0010Y\u001a\u00020\u000f2\u0006\u0010<\u001a\u00020\u000fH\u0002J\u0016\u0010Z\u001a\u00020-2\u0006\u0010F\u001a\u00020\u0016H\u0082@\u00a2\u0006\u0002\u0010[J\u0010\u0010\\\u001a\u00020\u000f2\u0006\u0010F\u001a\u00020\u0016H\u0002J\u0010\u0010]\u001a\u00020-2\u0006\u0010F\u001a\u00020\u0016H\u0002R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006_"}, d2 = {"Lcom/example/abonekaptanmobile/services/SubscriptionClassifier;", "", "communityPatternRepo", "Lcom/example/abonekaptanmobile/data/repository/CommunityPatternRepository;", "huggingFaceRepository", "Lcom/example/abonekaptanmobile/data/repository/HuggingFaceRepository;", "(Lcom/example/abonekaptanmobile/data/repository/CommunityPatternRepository;Lcom/example/abonekaptanmobile/data/repository/HuggingFaceRepository;)V", "cancelPatterns", "", "Lkotlin/text/Regex;", "inactivityThresholdDays", "", "inactivityThresholdMillis", "paidSubscriptionCompanies", "", "", "paymentPatterns", "promotionalPatterns", "startPatterns", "trustedSubscriptionDomains", "applyAndCollectSubscriptionPatterns", "", "Lcom/example/abonekaptanmobile/model/RawEmail;", "emails", "subscriptionPatterns", "Lcom/example/abonekaptanmobile/data/local/entity/SubscriptionPatternEntity;", "collector", "", "Lcom/example/abonekaptanmobile/model/ClassifiedEmail;", "classifyEmails", "Lcom/example/abonekaptanmobile/model/SubscriptionItem;", "allRawEmails", "onProgress", "Lkotlin/Function2;", "", "Lkotlin/ParameterName;", "name", "progress", "status", "", "(Ljava/util/List;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "classifyEmailsTwoStage", "convertAIResultToHybridValidation", "Lcom/example/abonekaptanmobile/data/remote/model/HybridValidationResult;", "aiResult", "Lcom/example/abonekaptanmobile/services/SubscriptionClassifier$AIClassificationResult;", "convertHybridEmailTypeToEmailType", "Lcom/example/abonekaptanmobile/model/EmailType;", "hybridEmailType", "createFallbackDetailedEmailTypeResult", "Lcom/example/abonekaptanmobile/data/remote/model/DetailedClassificationResult;", "emailContent", "createFallbackEmailTypeResult", "Lcom/example/abonekaptanmobile/data/remote/model/ClassificationResult;", "createFallbackValidation", "companyResult", "emailTypeResult", "createLocalCompanyResult", "domain", "createLocalDetailedEmailTypeResult", "companyName", "createLocalValidation", "createSubscriptionItemFromFinalStatus", "Lcom/example/abonekaptanmobile/data/remote/model/FinalSubscriptionStatus;", "allEmails", "createSubscriptionItemsFromDetails", "classifiedEmailDetails", "", "(Ljava/util/Map;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "determineEmailType", "email", "determineEmailTypeFromPatterns", "determineServiceName", "patterns", "determineSubscriptionType", "Lcom/example/abonekaptanmobile/model/SubscriptionType;", "subscriptionResult", "paidSubscriptionResult", "extractDomain", "emailAddress", "extractGeneralServiceName", "from", "subject", "bodySnippet", "isPotentiallyReliableSenderForHeuristics", "", "isTrustedSubscriptionDomain", "matchesPattern", "pattern", "normalizeCompanyName", "performAdvancedAIAnalysis", "(Lcom/example/abonekaptanmobile/model/RawEmail;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "prepareEmailContentForClassification", "simulateAdvancedAIResponse", "AIClassificationResult", "app_debug"})
public final class SubscriptionClassifier {
    @org.jetbrains.annotations.NotNull()
    private final com.example.abonekaptanmobile.data.repository.CommunityPatternRepository communityPatternRepo = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.abonekaptanmobile.data.repository.HuggingFaceRepository huggingFaceRepository = null;
    private final long inactivityThresholdDays = 90L;
    private final long inactivityThresholdMillis = 0L;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<kotlin.text.Regex> cancelPatterns = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<kotlin.text.Regex> startPatterns = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<kotlin.text.Regex> paymentPatterns = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<kotlin.text.Regex> promotionalPatterns = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Set<java.lang.String> trustedSubscriptionDomains = null;
    
    /**
     * Ücretli abonelik şirketleri listesi - sadece bu şirketler abonelik olarak değerlendirilir
     */
    @org.jetbrains.annotations.NotNull()
    private final java.util.Set<java.lang.String> paidSubscriptionCompanies = null;
    
    @javax.inject.Inject()
    public SubscriptionClassifier(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.repository.CommunityPatternRepository communityPatternRepo, @org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.repository.HuggingFaceRepository huggingFaceRepository) {
        super();
    }
    
    /**
     * Turkish: E-postaları sınıflandırır ve abonelik öğelerini oluşturur.
     * English: Classifies emails and creates subscription items.
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object classifyEmails(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.abonekaptanmobile.model.RawEmail> allRawEmails, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function2<? super java.lang.Float, ? super java.lang.String, kotlin.Unit> onProgress, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.abonekaptanmobile.model.SubscriptionItem>> $completion) {
        return null;
    }
    
    /**
     * Turkish: E-posta türünü belirler.
     * English: Determines the email type.
     */
    private final com.example.abonekaptanmobile.model.EmailType determineEmailType(com.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult emailTypeResult, com.example.abonekaptanmobile.model.RawEmail email) {
        return null;
    }
    
    /**
     * Turkish: Kalıplara göre e-posta türünü belirler.
     * English: Determines the email type based on patterns.
     */
    private final com.example.abonekaptanmobile.model.EmailType determineEmailTypeFromPatterns(com.example.abonekaptanmobile.model.RawEmail email) {
        return null;
    }
    
    /**
     * Turkish: Hybrid Approach email türünü EmailType enum'una dönüştürür.
     * English: Converts Hybrid Approach email type to EmailType enum.
     */
    private final com.example.abonekaptanmobile.model.EmailType convertHybridEmailTypeToEmailType(java.lang.String hybridEmailType) {
        return null;
    }
    
    /**
     * Turkish: Abonelik türünü belirler.
     * English: Determines the subscription type.
     */
    private final com.example.abonekaptanmobile.model.SubscriptionType determineSubscriptionType(com.example.abonekaptanmobile.data.remote.model.ClassificationResult subscriptionResult, com.example.abonekaptanmobile.data.remote.model.ClassificationResult paidSubscriptionResult) {
        return null;
    }
    
    /**
     * E-posta içeriğini sınıflandırma için hazırlar
     */
    private final java.lang.String prepareEmailContentForClassification(com.example.abonekaptanmobile.model.RawEmail email) {
        return null;
    }
    
    /**
     * E-posta için servis adını belirler
     */
    private final java.lang.String determineServiceName(com.example.abonekaptanmobile.model.RawEmail email, java.util.List<com.example.abonekaptanmobile.data.local.entity.SubscriptionPatternEntity> patterns) {
        return null;
    }
    
    /**
     * E-posta adresinden domain adını çıkarır
     */
    private final java.lang.String extractDomain(java.lang.String emailAddress) {
        return null;
    }
    
    /**
     * Şirket adlarını normalize eder - duplikasyonları önler
     */
    private final java.lang.String normalizeCompanyName(java.lang.String companyName) {
        return null;
    }
    
    /**
     * TÜM DOMAIN'LERİ KABUL ET - AI sınıflandırması yapacak
     */
    private final boolean isTrustedSubscriptionDomain(java.lang.String domain) {
        return false;
    }
    
    /**
     * AI destekli şirket sınıflandırması - sadece ücretli abonelik şirketlerini tespit eder
     * GÜÇLENDIRILMIŞ VERSİYON: Daha detaylı analiz
     */
    private final com.example.abonekaptanmobile.data.remote.model.ClassificationResult createLocalCompanyResult(java.lang.String domain) {
        return null;
    }
    
    /**
     * HuggingFace API hatası durumunda fallback email türü sonucu oluşturur
     */
    private final com.example.abonekaptanmobile.data.remote.model.ClassificationResult createFallbackEmailTypeResult(java.lang.String emailContent) {
        return null;
    }
    
    /**
     * Yerel detaylı email türü sınıflandırması - gelişmiş pattern matching
     * GÜÇLENDIRILMIŞ VERSİYON: Daha kapsamlı ve detaylı analiz
     */
    private final com.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult createLocalDetailedEmailTypeResult(java.lang.String emailContent, java.lang.String companyName) {
        return null;
    }
    
    /**
     * HuggingFace API hatası durumunda fallback detaylı email türü sonucu oluşturur
     */
    private final com.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult createFallbackDetailedEmailTypeResult(java.lang.String emailContent) {
        return null;
    }
    
    /**
     * 🤖 Gelişmiş AI analizi - Gerçek AI ile detaylı e-posta analizi
     */
    private final java.lang.Object performAdvancedAIAnalysis(com.example.abonekaptanmobile.model.RawEmail email, kotlin.coroutines.Continuation<? super com.example.abonekaptanmobile.services.SubscriptionClassifier.AIClassificationResult> $completion) {
        return null;
    }
    
    /**
     * 🤖 Gelişmiş AI analizi simülasyonu - Gerçek AI mantığı
     */
    private final com.example.abonekaptanmobile.services.SubscriptionClassifier.AIClassificationResult simulateAdvancedAIResponse(com.example.abonekaptanmobile.model.RawEmail email) {
        return null;
    }
    
    /**
     * AI sonucunu hybrid validation format'ına dönüştür
     */
    private final com.example.abonekaptanmobile.data.remote.model.HybridValidationResult convertAIResultToHybridValidation(com.example.abonekaptanmobile.services.SubscriptionClassifier.AIClassificationResult aiResult) {
        return null;
    }
    
    /**
     * Yerel doğrulama sistemi - gelişmiş güvenilirlik kontrolü
     */
    private final com.example.abonekaptanmobile.data.remote.model.HybridValidationResult createLocalValidation(com.example.abonekaptanmobile.data.remote.model.ClassificationResult companyResult, com.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult emailTypeResult) {
        return null;
    }
    
    /**
     * HuggingFace API hatası durumunda fallback doğrulama sonucu oluşturur
     */
    private final com.example.abonekaptanmobile.data.remote.model.HybridValidationResult createFallbackValidation(com.example.abonekaptanmobile.data.remote.model.ClassificationResult companyResult, com.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult emailTypeResult) {
        return null;
    }
    
    private final boolean matchesPattern(com.example.abonekaptanmobile.model.RawEmail email, com.example.abonekaptanmobile.data.local.entity.SubscriptionPatternEntity pattern) {
        return false;
    }
    
    private final java.util.List<com.example.abonekaptanmobile.model.RawEmail> applyAndCollectSubscriptionPatterns(java.util.List<com.example.abonekaptanmobile.model.RawEmail> emails, java.util.List<com.example.abonekaptanmobile.data.local.entity.SubscriptionPatternEntity> subscriptionPatterns, java.util.Map<java.lang.String, java.util.List<com.example.abonekaptanmobile.model.ClassifiedEmail>> collector) {
        return null;
    }
    
    /**
     * Turkish: Sınıflandırılmış e-postalardan abonelik öğelerini oluşturur.
     * English: Creates subscription items from classified emails.
     */
    private final java.lang.Object createSubscriptionItemsFromDetails(java.util.Map<java.lang.String, ? extends java.util.List<com.example.abonekaptanmobile.model.ClassifiedEmail>> classifiedEmailDetails, kotlin.coroutines.Continuation<? super java.util.List<com.example.abonekaptanmobile.model.SubscriptionItem>> $completion) {
        return null;
    }
    
    private final java.lang.String extractGeneralServiceName(java.lang.String from, java.lang.String subject, java.lang.String bodySnippet) {
        return null;
    }
    
    private final boolean isPotentiallyReliableSenderForHeuristics(java.lang.String from) {
        return false;
    }
    
    /**
     * Turkish: YENİ İKİ AŞAMALI SİSTEM - E-postaları iki aşamada analiz eder.
     * English: NEW TWO-STAGE SYSTEM - Analyzes emails in two stages.
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object classifyEmailsTwoStage(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.abonekaptanmobile.model.RawEmail> allRawEmails, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function2<? super java.lang.Float, ? super java.lang.String, kotlin.Unit> onProgress, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.abonekaptanmobile.model.SubscriptionItem>> $completion) {
        return null;
    }
    
    /**
     * Turkish: FinalSubscriptionStatus'tan SubscriptionItem oluşturur.
     * English: Creates SubscriptionItem from FinalSubscriptionStatus.
     */
    private final com.example.abonekaptanmobile.model.SubscriptionItem createSubscriptionItemFromFinalStatus(com.example.abonekaptanmobile.data.remote.model.FinalSubscriptionStatus status, java.util.List<com.example.abonekaptanmobile.model.RawEmail> allEmails) {
        return null;
    }
    
    /**
     * AI Classification Result data class
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0002\b\u000e\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B%\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\bJ\t\u0010\u000f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0003H\u00c6\u0003J1\u0010\u0013\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u0014\u001a\u00020\u00152\b\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0017\u001a\u00020\u0018H\u00d6\u0001J\t\u0010\u0019\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\nR\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\n\u00a8\u0006\u001a"}, d2 = {"Lcom/example/abonekaptanmobile/services/SubscriptionClassifier$AIClassificationResult;", "", "company", "", "emailType", "confidence", "", "reasoning", "(Ljava/lang/String;Ljava/lang/String;DLjava/lang/String;)V", "getCompany", "()Ljava/lang/String;", "getConfidence", "()D", "getEmailType", "getReasoning", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
    public static final class AIClassificationResult {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String company = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String emailType = null;
        private final double confidence = 0.0;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String reasoning = null;
        
        public AIClassificationResult(@org.jetbrains.annotations.NotNull()
        java.lang.String company, @org.jetbrains.annotations.NotNull()
        java.lang.String emailType, double confidence, @org.jetbrains.annotations.NotNull()
        java.lang.String reasoning) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getCompany() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getEmailType() {
            return null;
        }
        
        public final double getConfidence() {
            return 0.0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getReasoning() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component2() {
            return null;
        }
        
        public final double component3() {
            return 0.0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component4() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.abonekaptanmobile.services.SubscriptionClassifier.AIClassificationResult copy(@org.jetbrains.annotations.NotNull()
        java.lang.String company, @org.jetbrains.annotations.NotNull()
        java.lang.String emailType, double confidence, @org.jetbrains.annotations.NotNull()
        java.lang.String reasoning) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}