// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.data.repository;

import com.example.abonekaptanmobile.data.remote.GmailApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GmailRepository_Factory implements Factory<GmailRepository> {
  private final Provider<GmailApi> gmailApiProvider;

  public GmailRepository_Factory(Provider<GmailApi> gmailApiProvider) {
    this.gmailApiProvider = gmailApiProvider;
  }

  @Override
  public GmailRepository get() {
    return newInstance(gmailApiProvider.get());
  }

  public static GmailRepository_Factory create(Provider<GmailApi> gmailApiProvider) {
    return new GmailRepository_Factory(gmailApiProvider);
  }

  public static GmailRepository newInstance(GmailApi gmailApi) {
    return new GmailRepository(gmailApi);
  }
}
