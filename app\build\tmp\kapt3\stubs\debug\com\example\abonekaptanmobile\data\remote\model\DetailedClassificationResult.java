package com.example.abonekaptanmobile.data.remote.model;

import com.google.gson.annotations.SerializedName;

/**
 * Turkish: Hugging Face API'sinden dönen detaylı sınıflandırma sonucu.
 * English: Detailed classification result returned from Hugging Face API.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0006\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001B#\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007\u00a2\u0006\u0002\u0010\tJ\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0005H\u00c6\u0003J\u000f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\b0\u0007H\u00c6\u0003J-\u0010\u0013\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007H\u00c6\u0001J\u0013\u0010\u0014\u001a\u00020\u00152\b\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u0014\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00030\u00072\u0006\u0010\u0018\u001a\u00020\u0005J\u000e\u0010\u0019\u001a\u00020\u00052\u0006\u0010\u001a\u001a\u00020\u0003J\t\u0010\u001b\u001a\u00020\u001cH\u00d6\u0001J\u0016\u0010\u001d\u001a\u00020\u00152\u0006\u0010\u001a\u001a\u00020\u00032\u0006\u0010\u0018\u001a\u00020\u0005J\t\u0010\u001e\u001a\u00020\u0003H\u00d6\u0001R\u0017\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000f\u00a8\u0006\u001f"}, d2 = {"Lcom/example/abonekaptanmobile/data/remote/model/DetailedClassificationResult;", "", "primaryLabel", "", "primaryScore", "", "allResults", "", "Lcom/example/abonekaptanmobile/data/remote/model/ClassificationResult;", "(Ljava/lang/String;FLjava/util/List;)V", "getAllResults", "()Ljava/util/List;", "getPrimaryLabel", "()Ljava/lang/String;", "getPrimaryScore", "()F", "component1", "component2", "component3", "copy", "equals", "", "other", "getLabelsAboveThreshold", "threshold", "getScoreForLabel", "label", "hashCode", "", "isLabelAboveThreshold", "toString", "app_debug"})
public final class DetailedClassificationResult {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String primaryLabel = null;
    private final float primaryScore = 0.0F;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.abonekaptanmobile.data.remote.model.ClassificationResult> allResults = null;
    
    public DetailedClassificationResult(@org.jetbrains.annotations.NotNull()
    java.lang.String primaryLabel, float primaryScore, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.abonekaptanmobile.data.remote.model.ClassificationResult> allResults) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getPrimaryLabel() {
        return null;
    }
    
    public final float getPrimaryScore() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.abonekaptanmobile.data.remote.model.ClassificationResult> getAllResults() {
        return null;
    }
    
    /**
     * Turkish: Belirli bir etiketin skorunu döndürür.
     * English: Returns the score for a specific label.
     */
    public final float getScoreForLabel(@org.jetbrains.annotations.NotNull()
    java.lang.String label) {
        return 0.0F;
    }
    
    /**
     * Turkish: Belirli bir eşik değerinin üzerinde olan etiketleri döndürür.
     * English: Returns labels that have a score above a certain threshold.
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getLabelsAboveThreshold(float threshold) {
        return null;
    }
    
    /**
     * Turkish: Belirli bir etiketin belirli bir eşik değerinin üzerinde olup olmadığını kontrol eder.
     * English: Checks if a specific label has a score above a certain threshold.
     */
    public final boolean isLabelAboveThreshold(@org.jetbrains.annotations.NotNull()
    java.lang.String label, float threshold) {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    public final float component2() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.abonekaptanmobile.data.remote.model.ClassificationResult> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult copy(@org.jetbrains.annotations.NotNull()
    java.lang.String primaryLabel, float primaryScore, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.abonekaptanmobile.data.remote.model.ClassificationResult> allResults) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}