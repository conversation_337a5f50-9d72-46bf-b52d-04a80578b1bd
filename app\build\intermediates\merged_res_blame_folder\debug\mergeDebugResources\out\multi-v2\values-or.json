{"logs": [{"outputFile": "com.example.abonekaptanmobile.app-mergeDebugResources-67:/values-or/values-or.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f17758470c37236a8702f22be0817404\\transformed\\play-services-basement-18.2.0\\res\\values-or\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "2059", "endColumns": "139", "endOffsets": "2194"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\758d06e28d876accf8cd86f1d399c64f\\transformed\\ui-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,381,481,567,644,742,830,917,987,1057,1135,1217,1287,1370,1437", "endColumns": "96,86,91,99,85,76,97,87,86,69,69,77,81,69,82,66,118", "endOffsets": "197,284,376,476,562,639,737,825,912,982,1052,1130,1212,1282,1365,1432,1551"}, "to": {"startLines": "9,10,29,30,31,32,33,90,91,92,93,94,95,96,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "840,937,3304,3396,3496,3582,3659,10008,10096,10183,10253,10323,10401,10483,10654,10737,10804", "endColumns": "96,86,91,99,85,76,97,87,86,69,69,77,81,69,82,66,118", "endOffsets": "932,1019,3391,3491,3577,3654,3752,10091,10178,10248,10318,10396,10478,10548,10732,10799,10918"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\44e19e1f9b56976b7f9e33316bf0ba7e\\transformed\\play-services-base-18.0.1\\res\\values-or\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,457,585,698,849,980,1090,1196,1359,1468,1625,1754,1900,2053,2114,2182", "endColumns": "106,156,127,112,150,130,109,105,162,108,156,128,145,152,60,67,82", "endOffsets": "299,456,584,697,848,979,1089,1195,1358,1467,1624,1753,1899,2052,2113,2181,2264"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1024,1135,1296,1428,1545,1700,1835,1949,2199,2366,2479,2640,2773,2923,3080,3145,3217", "endColumns": "110,160,131,116,154,134,113,109,166,112,160,132,149,156,64,71,86", "endOffsets": "1130,1291,1423,1540,1695,1830,1944,2054,2361,2474,2635,2768,2918,3075,3140,3212,3299"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ae9863232b9716e1e6ec45d64efe02de\\transformed\\foundation-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,87", "endOffsets": "135,223"}, "to": {"startLines": "101,102", "startColumns": "4,4", "startOffsets": "10923,11008", "endColumns": "84,87", "endOffsets": "11003,11091"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0ddf931afad622e529ec9d528ae33174\\transformed\\core-1.12.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "2,3,4,5,6,7,8,97", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,208,310,413,518,619,721,10553", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "203,305,408,513,614,716,835,10649"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1062e727b039c203a5f49af9a364889\\transformed\\material3-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,294,407,526,620,720,837,980,1106,1257,1342,1447,1543,1638,1754,1884,1994,2137,2275,2406,2598,2724,2853,2988,3118,3215,3311,3428,3550,3655,3760,3863,4005,4155,4262,4371,4446,4550,4652,4746,4837,4942,5022,5107,5208,5314,5407,5508,5595,5703,5802,5905,6029,6109,6212", "endColumns": "121,116,112,118,93,99,116,142,125,150,84,104,95,94,115,129,109,142,137,130,191,125,128,134,129,96,95,116,121,104,104,102,141,149,106,108,74,103,101,93,90,104,79,84,100,105,92,100,86,107,98,102,123,79,102,93", "endOffsets": "172,289,402,521,615,715,832,975,1101,1252,1337,1442,1538,1633,1749,1879,1989,2132,2270,2401,2593,2719,2848,2983,3113,3210,3306,3423,3545,3650,3755,3858,4000,4150,4257,4366,4441,4545,4647,4741,4832,4937,5017,5102,5203,5309,5402,5503,5590,5698,5797,5900,6024,6104,6207,6301"}, "to": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3757,3879,3996,4109,4228,4322,4422,4539,4682,4808,4959,5044,5149,5245,5340,5456,5586,5696,5839,5977,6108,6300,6426,6555,6690,6820,6917,7013,7130,7252,7357,7462,7565,7707,7857,7964,8073,8148,8252,8354,8448,8539,8644,8724,8809,8910,9016,9109,9210,9297,9405,9504,9607,9731,9811,9914", "endColumns": "121,116,112,118,93,99,116,142,125,150,84,104,95,94,115,129,109,142,137,130,191,125,128,134,129,96,95,116,121,104,104,102,141,149,106,108,74,103,101,93,90,104,79,84,100,105,92,100,86,107,98,102,123,79,102,93", "endOffsets": "3874,3991,4104,4223,4317,4417,4534,4677,4803,4954,5039,5144,5240,5335,5451,5581,5691,5834,5972,6103,6295,6421,6550,6685,6815,6912,7008,7125,7247,7352,7457,7560,7702,7852,7959,8068,8143,8247,8349,8443,8534,8639,8719,8804,8905,9011,9104,9205,9292,9400,9499,9602,9726,9806,9909,10003"}}]}]}