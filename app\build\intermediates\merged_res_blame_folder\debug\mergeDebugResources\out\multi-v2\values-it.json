{"logs": [{"outputFile": "com.example.abonekaptanmobile.app-mergeDebugResources-67:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\758d06e28d876accf8cd86f1d399c64f\\transformed\\ui-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,292,390,490,577,656,762,855,950,1015,1079,1163,1251,1336,1414,1483", "endColumns": "99,86,97,99,86,78,105,92,94,64,63,83,87,84,77,68,120", "endOffsets": "200,287,385,485,572,651,757,850,945,1010,1074,1158,1246,1331,1409,1478,1599"}, "to": {"startLines": "9,10,29,30,31,32,33,90,91,92,93,94,95,96,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "852,952,3261,3359,3459,3546,3625,10038,10131,10226,10291,10355,10439,10527,10713,10791,10860", "endColumns": "99,86,97,99,86,78,105,92,94,64,63,83,87,84,77,68,120", "endOffsets": "947,1034,3354,3454,3541,3620,3726,10126,10221,10286,10350,10434,10522,10607,10786,10855,10976"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ae9863232b9716e1e6ec45d64efe02de\\transformed\\foundation-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,153", "endColumns": "97,98", "endOffsets": "148,247"}, "to": {"startLines": "101,102", "startColumns": "4,4", "startOffsets": "10981,11079", "endColumns": "97,98", "endOffsets": "11074,11173"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1062e727b039c203a5f49af9a364889\\transformed\\material3-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,306,428,552,657,753,866,1009,1128,1286,1370,1482,1576,1676,1795,1917,2034,2176,2316,2459,2635,2770,2890,3013,3143,3238,3335,3462,3600,3700,3810,3916,4059,4207,4317,4418,4507,4603,4696,4782,4868,4971,5051,5134,5233,5339,5439,5540,5628,5738,5838,5943,6061,6141,6255", "endColumns": "126,123,121,123,104,95,112,142,118,157,83,111,93,99,118,121,116,141,139,142,175,134,119,122,129,94,96,126,137,99,109,105,142,147,109,100,88,95,92,85,85,102,79,82,98,105,99,100,87,109,99,104,117,79,113,106", "endOffsets": "177,301,423,547,652,748,861,1004,1123,1281,1365,1477,1571,1671,1790,1912,2029,2171,2311,2454,2630,2765,2885,3008,3138,3233,3330,3457,3595,3695,3805,3911,4054,4202,4312,4413,4502,4598,4691,4777,4863,4966,5046,5129,5228,5334,5434,5535,5623,5733,5833,5938,6056,6136,6250,6357"}, "to": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3731,3858,3982,4104,4228,4333,4429,4542,4685,4804,4962,5046,5158,5252,5352,5471,5593,5710,5852,5992,6135,6311,6446,6566,6689,6819,6914,7011,7138,7276,7376,7486,7592,7735,7883,7993,8094,8183,8279,8372,8458,8544,8647,8727,8810,8909,9015,9115,9216,9304,9414,9514,9619,9737,9817,9931", "endColumns": "126,123,121,123,104,95,112,142,118,157,83,111,93,99,118,121,116,141,139,142,175,134,119,122,129,94,96,126,137,99,109,105,142,147,109,100,88,95,92,85,85,102,79,82,98,105,99,100,87,109,99,104,117,79,113,106", "endOffsets": "3853,3977,4099,4223,4328,4424,4537,4680,4799,4957,5041,5153,5247,5347,5466,5588,5705,5847,5987,6130,6306,6441,6561,6684,6814,6909,7006,7133,7271,7371,7481,7587,7730,7878,7988,8089,8178,8274,8367,8453,8539,8642,8722,8805,8904,9010,9110,9211,9299,9409,9509,9614,9732,9812,9926,10033"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\44e19e1f9b56976b7f9e33316bf0ba7e\\transformed\\play-services-base-18.0.1\\res\\values-it\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,441,562,666,820,944,1060,1160,1313,1416,1566,1689,1841,2018,2081,2138", "endColumns": "100,146,120,103,153,123,115,99,152,102,149,122,151,176,62,56,72", "endOffsets": "293,440,561,665,819,943,1059,1159,1312,1415,1565,1688,1840,2017,2080,2137,2210"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1039,1144,1295,1420,1528,1686,1814,1934,2174,2331,2438,2592,2719,2875,3056,3123,3184", "endColumns": "104,150,124,107,157,127,119,103,156,106,153,126,155,180,66,60,76", "endOffsets": "1139,1290,1415,1523,1681,1809,1929,2033,2326,2433,2587,2714,2870,3051,3118,3179,3256"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f17758470c37236a8702f22be0817404\\transformed\\play-services-basement-18.2.0\\res\\values-it\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "131", "endOffsets": "326"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "2038", "endColumns": "135", "endOffsets": "2169"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0ddf931afad622e529ec9d528ae33174\\transformed\\core-1.12.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "2,3,4,5,6,7,8,97", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,404,506,615,722,10612", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "198,300,399,501,610,717,847,10708"}}]}]}