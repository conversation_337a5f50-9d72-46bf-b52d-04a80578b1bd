package com.example.abonekaptanmobile.workers;

import androidx.hilt.work.WorkerAssistedFactory;
import androidx.work.ListenableWorker;
import dagger.Binds;
import dagger.Module;
import dagger.hilt.InstallIn;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.components.SingletonComponent;
import dagger.multibindings.IntoMap;
import dagger.multibindings.StringKey;

@Module
@InstallIn(SingletonComponent.class)
@OriginatingElement(
    topLevelClass = ProcessFeedbackWorker.class
)
public interface ProcessFeedbackWorker_HiltModule {
  @Binds
  @IntoMap
  @StringKey("com.example.abonekaptanmobile.workers.ProcessFeedbackWorker")
  WorkerAssistedFactory<? extends ListenableWorker> bind(
      ProcessFeedbackWorker_AssistedFactory factory);
}
