// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.workers;

import android.content.Context;
import androidx.work.WorkerParameters;
import dagger.internal.DaggerGenerated;
import dagger.internal.InstanceFactory;
import javax.inject.Provider;

@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ProcessFeedbackWorker_AssistedFactory_Impl implements ProcessFeedbackWorker_AssistedFactory {
  private final ProcessFeedbackWorker_Factory delegateFactory;

  ProcessFeedbackWorker_AssistedFactory_Impl(ProcessFeedbackWorker_Factory delegateFactory) {
    this.delegateFactory = delegateFactory;
  }

  @Override
  public ProcessFeedbackWorker create(Context arg0, WorkerParameters arg1) {
    return delegateFactory.get(arg0, arg1);
  }

  public static Provider<ProcessFeedbackWorker_AssistedFactory> create(
      ProcessFeedbackWorker_Factory delegateFactory) {
    return InstanceFactory.create(new ProcessFeedbackWorker_AssistedFactory_Impl(delegateFactory));
  }
}
