package com.example.abonekaptanmobile.ui.screens;

import androidx.compose.foundation.ExperimentalFoundationApi;
import androidx.compose.foundation.layout.*;
import androidx.compose.material.icons.Icons;
import androidx.compose.material3.*;
import androidx.compose.runtime.*;
import androidx.compose.ui.Alignment;
import androidx.compose.ui.Modifier;
import androidx.compose.ui.text.font.FontWeight;
import com.example.abonekaptanmobile.R;
import com.example.abonekaptanmobile.model.SubscriptionItem;
import com.example.abonekaptanmobile.model.SubscriptionStatus;
import com.example.abonekaptanmobile.ui.viewmodel.MainViewModel;
import java.text.SimpleDateFormat;
import java.util.*;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000$\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\u001a$\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a\u0010\u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0007\u001a\u00020\bH\u0007\u001a\u0010\u0010\t\u001a\u00020\u00012\u0006\u0010\n\u001a\u00020\u000bH\u0007\u00a8\u0006\f"}, d2 = {"SubscriptionCard", "", "item", "Lcom/example/abonekaptanmobile/model/SubscriptionItem;", "onFeedbackClick", "Lkotlin/Function1;", "SubscriptionHeader", "title", "", "SubscriptionListScreen", "viewModel", "Lcom/example/abonekaptanmobile/ui/viewmodel/MainViewModel;", "app_debug"})
public final class SubscriptionListScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.foundation.ExperimentalFoundationApi.class})
    @androidx.compose.runtime.Composable()
    public static final void SubscriptionListScreen(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.ui.viewmodel.MainViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SubscriptionHeader(@org.jetbrains.annotations.NotNull()
    java.lang.String title) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SubscriptionCard(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.model.SubscriptionItem item, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.abonekaptanmobile.model.SubscriptionItem, kotlin.Unit> onFeedbackClick) {
    }
}