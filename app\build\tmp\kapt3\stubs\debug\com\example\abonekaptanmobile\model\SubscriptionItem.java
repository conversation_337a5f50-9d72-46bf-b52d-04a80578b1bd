package com.example.abonekaptanmobile.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u001c\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001BK\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u0007\u0012\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00030\f\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\u0002\u0010\u000eJ\t\u0010\u001f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010 \u001a\u00020\u0005H\u00c6\u0003J\t\u0010!\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\"\u001a\u00020\tH\u00c6\u0003J\u0010\u0010#\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0010J\u000f\u0010$\u001a\b\u0012\u0004\u0012\u00020\u00030\fH\u00c6\u0003J\u0010\u0010%\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0010J^\u0010&\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u00072\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00030\f2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u0007H\u00c6\u0001\u00a2\u0006\u0002\u0010\'J\u0013\u0010(\u001a\u00020)2\b\u0010*\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010+\u001a\u00020\u0005H\u00d6\u0001J\t\u0010,\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\n\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\n\n\u0002\u0010\u0011\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00030\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u001a\u0010\b\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001a\u0010\u001b\"\u0004\b\u001c\u0010\u001dR\u0015\u0010\r\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\n\n\u0002\u0010\u0011\u001a\u0004\b\u001e\u0010\u0010\u00a8\u0006-"}, d2 = {"Lcom/example/abonekaptanmobile/model/SubscriptionItem;", "", "serviceName", "", "emailCount", "", "lastEmailDate", "", "status", "Lcom/example/abonekaptanmobile/model/SubscriptionStatus;", "cancellationDate", "relatedEmailIds", "", "subscriptionStartDate", "(Ljava/lang/String;IJLcom/example/abonekaptanmobile/model/SubscriptionStatus;Ljava/lang/Long;Ljava/util/List;Ljava/lang/Long;)V", "getCancellationDate", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getEmailCount", "()I", "getLastEmailDate", "()J", "getRelatedEmailIds", "()Ljava/util/List;", "getServiceName", "()Ljava/lang/String;", "getStatus", "()Lcom/example/abonekaptanmobile/model/SubscriptionStatus;", "setStatus", "(Lcom/example/abonekaptanmobile/model/SubscriptionStatus;)V", "getSubscriptionStartDate", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "(Ljava/lang/String;IJLcom/example/abonekaptanmobile/model/SubscriptionStatus;Ljava/lang/Long;Ljava/util/List;Ljava/lang/Long;)Lcom/example/abonekaptanmobile/model/SubscriptionItem;", "equals", "", "other", "hashCode", "toString", "app_debug"})
public final class SubscriptionItem {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String serviceName = null;
    private final int emailCount = 0;
    private final long lastEmailDate = 0L;
    @org.jetbrains.annotations.NotNull()
    private com.example.abonekaptanmobile.model.SubscriptionStatus status;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Long cancellationDate = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> relatedEmailIds = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Long subscriptionStartDate = null;
    
    public SubscriptionItem(@org.jetbrains.annotations.NotNull()
    java.lang.String serviceName, int emailCount, long lastEmailDate, @org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.model.SubscriptionStatus status, @org.jetbrains.annotations.Nullable()
    java.lang.Long cancellationDate, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> relatedEmailIds, @org.jetbrains.annotations.Nullable()
    java.lang.Long subscriptionStartDate) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getServiceName() {
        return null;
    }
    
    public final int getEmailCount() {
        return 0;
    }
    
    public final long getLastEmailDate() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.model.SubscriptionStatus getStatus() {
        return null;
    }
    
    public final void setStatus(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.model.SubscriptionStatus p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long getCancellationDate() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getRelatedEmailIds() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long getSubscriptionStartDate() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final long component3() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.model.SubscriptionStatus component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.model.SubscriptionItem copy(@org.jetbrains.annotations.NotNull()
    java.lang.String serviceName, int emailCount, long lastEmailDate, @org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.model.SubscriptionStatus status, @org.jetbrains.annotations.Nullable()
    java.lang.Long cancellationDate, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> relatedEmailIds, @org.jetbrains.annotations.Nullable()
    java.lang.Long subscriptionStartDate) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}