-- Merging decision tree log ---
provider#androidx.startup.InitializationProvider
INJECTED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:43:9-52:20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f93d72572c8f1eb0463681a76a9f82c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f93d72572c8f1eb0463681a76a9f82c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f20881ad2ecbf951bde9e37fb1a34d4\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f20881ad2ecbf951bde9e37fb1a34d4\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bc77b08baa5e058442f01d3b8740064\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bc77b08baa5e058442f01d3b8740064\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:47:13-31
	android:authorities
		INJECTED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:45:13-68
	android:exported
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:46:13-37
	android:name
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:44:13-67
manifest
ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:2:1-55:12
INJECTED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:2:1-55:12
INJECTED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:2:1-55:12
INJECTED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:2:1-55:12
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:17:1-40:12
MERGED from [androidx.hilt:hilt-work:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\412b6ef651f14c8c1b4198b3e1b4f667\transformed\hilt-work-1.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0862665a5a0d04caaacd7ad9f122f17\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ca8f90c468c8ce971b2db927d0c0fa0\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:hilt-android:2.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6985aff9e5a96a24fdc982f9c2cbad06\transformed\hilt-android-2.49\AndroidManifest.xml:16:1-19:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ab3f8438605bbe9b79dfed51418bd4\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0736e4ddbe12619fb8da75e9ceb1173b\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db3a7b6af74cf1429029b1abe1c432ca\transformed\play-services-fido-20.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44e19e1f9b56976b7f9e33316bf0ba7e\transformed\play-services-base-18.0.1\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6968c742713d6dc1de5ff4821fe15f32\transformed\play-services-tasks-18.0.1\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f17758470c37236a8702f22be0817404\transformed\play-services-basement-18.2.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9c34032e693f558d3bfaaeb1f82e9ed\transformed\fragment-1.5.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02d433c1203c1af9e1f7f0e1ce24e13\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98411e9c9435da7f16fa3304181eecc7\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.navigation:navigation-compose:2.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f82e8fd986dc7a55ada9e5aad87ba648\transformed\navigation-compose-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1062e727b039c203a5f49af9a364889\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02069888148991c8215125a6eb160e6\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\441006add3d247ad75ec852593ccf6f8\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7512fb7757540f965633433483f2aea8\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\93ea021be202f37b2a37351cc45d3653\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae9863232b9716e1e6ec45d64efe02de\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc04b051877837630ff27043bddcc536\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4606d9d9f45968c7915b277b29a9662\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a40bf90101796a14a05da6bf9d6b8fe\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16b71b3088e9666a0519ca03cd7fb2af\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ff4138f34d916ab64efc1ad9ff9bbe4\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ffdefbe13277925eff2fd0eb5e23821\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a47a47129b71dadb07682124089ee59\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da9ca9f265ea25a5c32e964881d59f17\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ac865f2e376b0e35b433497d52ff6a4\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\066a0ad3a2dc6f9cdea2e59ec7063442\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbbb40529799f840f5efdd34f185d49\transformed\ui-test-manifest-1.6.2\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f6650a0e991f193d9beef7fe5f16423\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f93d72572c8f1eb0463681a76a9f82c\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f20881ad2ecbf951bde9e37fb1a34d4\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adf2c11f76fef535c1060fff21c61aed\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7de5344c12143cca11a0ab5e318bcc73\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8b76eb325d7efbddd4cb92c49c77079\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a09d6bd8eaa5be3f4376ce9ed7cf1b1\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f60cddd10e3adb78377e455819e0e7db\transformed\navigation-runtime-ktx-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e9e2d1cf43cde8a32196cdda01a6068\transformed\navigation-runtime-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-common-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46ad2304e64c5a702977fad6696ea04d\transformed\navigation-common-ktx-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-common:2.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd203b27384ed6abbecfa6eb1dbc818e\transformed\navigation-common-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47c174996be18f1bb84fe8d589527704\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62e51bf216bfe5af090a1433c05af959\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67663ff03f7437bfd40a417d66c88a5b\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\058afe60a62462f8a077b2f93aad827c\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b9074ea43515050688821caed3e085a\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fdc6090eb890ea60e9895ac2d90c5f54\transformed\lifecycle-runtime-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\755b1088b89d0e690888cbb7a34e5b94\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6df12ac5967402e13f4181ddc3c39571\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\553fdf91606a07a84412c88833cf4ae2\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\758d06e28d876accf8cd86f1d399c64f\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03425cb92fd71ca562259853bf49cb47\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98d0104178181e4d53999be75c6c66be\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ac9d68b92fe2ab1028cb5e8476ae3bd\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ee1bd6a009846944d541900d2397703\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d8da8d98afa6f240c0f54189b1a77c0\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f8512599b3d55e93013e5ec39d4c7\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\736535d916a2f202cc5de9983b95bb18\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f0600dfc23bb70309fa0471f8b174b8\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f7d0d13b3d445bb9dc84bbc73ccbf0\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\38c461e1a459c353e8424d45ad6935e3\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82f47d1133a0d23573d29136b38f5ad5\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2366fe92f73a3c88d21f4742a2b39a90\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bbf81b3fa31fd696ace58d247b1e5d5\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f69793395e8c81844fe849a95e93ba9\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a5fc50ae51481312102ab347d031230\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e6074fcf09995d920a59ab173fea8ae\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a869f15ba42402e929d2b68c92b9e30\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32c7557b32a33e18709389bce01f0583\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bc77b08baa5e058442f01d3b8740064\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d88d238a2bf956356ce0d4cd266b5363\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.dagger:dagger-lint-aar:2.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\93876383fd2f0373b1a38e7e8869f4f8\transformed\dagger-lint-aar-2.49\AndroidManifest.xml:16:1-19:12
	package
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:4:5-44
		INJECTED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
	android:name
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.GET_ACCOUNTS
ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:11:5-71
	android:name
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:11:22-68
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:12:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
	android:name
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:12:22-65
application
ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:14:5-54:19
INJECTED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:14:5-54:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db3a7b6af74cf1429029b1abe1c432ca\transformed\play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db3a7b6af74cf1429029b1abe1c432ca\transformed\play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44e19e1f9b56976b7f9e33316bf0ba7e\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44e19e1f9b56976b7f9e33316bf0ba7e\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6968c742713d6dc1de5ff4821fe15f32\transformed\play-services-tasks-18.0.1\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6968c742713d6dc1de5ff4821fe15f32\transformed\play-services-tasks-18.0.1\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f17758470c37236a8702f22be0817404\transformed\play-services-basement-18.2.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f17758470c37236a8702f22be0817404\transformed\play-services-basement-18.2.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ac865f2e376b0e35b433497d52ff6a4\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ac865f2e376b0e35b433497d52ff6a4\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbbb40529799f840f5efdd34f185d49\transformed\ui-test-manifest-1.6.2\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbbb40529799f840f5efdd34f185d49\transformed\ui-test-manifest-1.6.2\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f93d72572c8f1eb0463681a76a9f82c\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f93d72572c8f1eb0463681a76a9f82c\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f20881ad2ecbf951bde9e37fb1a34d4\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f20881ad2ecbf951bde9e37fb1a34d4\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f7d0d13b3d445bb9dc84bbc73ccbf0\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f7d0d13b3d445bb9dc84bbc73ccbf0\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e6074fcf09995d920a59ab173fea8ae\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e6074fcf09995d920a59ab173fea8ae\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bc77b08baa5e058442f01d3b8740064\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bc77b08baa5e058442f01d3b8740064\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml
	android:roundIcon
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:21:9-54
	android:icon
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:19:9-43
	android:networkSecurityConfig
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:24:9-69
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:22:9-35
	android:label
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:20:9-41
	android:fullBackupContent
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:18:9-54
	tools:targetApi
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:26:9-29
	android:allowBackup
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:16:9-35
	android:theme
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:23:9-55
	android:dataExtractionRules
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:17:9-65
	android:usesCleartextTraffic
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:25:9-44
	android:name
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:15:9-39
activity#com.example.abonekaptanmobile.MainActivity
ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:27:9-40:20
	android:label
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:30:13-45
	android:exported
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:29:13-36
	android:theme
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:31:13-59
	android:name
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:28:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:32:13-35:29
action#android.intent.action.MAIN
ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:33:17-69
	android:name
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:33:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:34:17-77
	android:name
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:34:27-74
meta-data#androidx.work.WorkManagerInitializer
ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:48:13-51:39
REJECTED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
	tools:node
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:51:17-36
	android:value
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:50:17-49
	android:name
		ADDED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml:49:17-68
uses-sdk
INJECTED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml
INJECTED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.hilt:hilt-work:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\412b6ef651f14c8c1b4198b3e1b4f667\transformed\hilt-work-1.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.hilt:hilt-work:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\412b6ef651f14c8c1b4198b3e1b4f667\transformed\hilt-work-1.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0862665a5a0d04caaacd7ad9f122f17\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0862665a5a0d04caaacd7ad9f122f17\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ca8f90c468c8ce971b2db927d0c0fa0\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ca8f90c468c8ce971b2db927d0c0fa0\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:hilt-android:2.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6985aff9e5a96a24fdc982f9c2cbad06\transformed\hilt-android-2.49\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6985aff9e5a96a24fdc982f9c2cbad06\transformed\hilt-android-2.49\AndroidManifest.xml:18:3-42
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ab3f8438605bbe9b79dfed51418bd4\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ab3f8438605bbe9b79dfed51418bd4\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0736e4ddbe12619fb8da75e9ceb1173b\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0736e4ddbe12619fb8da75e9ceb1173b\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db3a7b6af74cf1429029b1abe1c432ca\transformed\play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db3a7b6af74cf1429029b1abe1c432ca\transformed\play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44e19e1f9b56976b7f9e33316bf0ba7e\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44e19e1f9b56976b7f9e33316bf0ba7e\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6968c742713d6dc1de5ff4821fe15f32\transformed\play-services-tasks-18.0.1\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6968c742713d6dc1de5ff4821fe15f32\transformed\play-services-tasks-18.0.1\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f17758470c37236a8702f22be0817404\transformed\play-services-basement-18.2.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f17758470c37236a8702f22be0817404\transformed\play-services-basement-18.2.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9c34032e693f558d3bfaaeb1f82e9ed\transformed\fragment-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9c34032e693f558d3bfaaeb1f82e9ed\transformed\fragment-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02d433c1203c1af9e1f7f0e1ce24e13\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02d433c1203c1af9e1f7f0e1ce24e13\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98411e9c9435da7f16fa3304181eecc7\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98411e9c9435da7f16fa3304181eecc7\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.navigation:navigation-compose:2.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f82e8fd986dc7a55ada9e5aad87ba648\transformed\navigation-compose-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-compose:2.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f82e8fd986dc7a55ada9e5aad87ba648\transformed\navigation-compose-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1062e727b039c203a5f49af9a364889\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1062e727b039c203a5f49af9a364889\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02069888148991c8215125a6eb160e6\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02069888148991c8215125a6eb160e6\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\441006add3d247ad75ec852593ccf6f8\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\441006add3d247ad75ec852593ccf6f8\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7512fb7757540f965633433483f2aea8\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7512fb7757540f965633433483f2aea8\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\93ea021be202f37b2a37351cc45d3653\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\93ea021be202f37b2a37351cc45d3653\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae9863232b9716e1e6ec45d64efe02de\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae9863232b9716e1e6ec45d64efe02de\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc04b051877837630ff27043bddcc536\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc04b051877837630ff27043bddcc536\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4606d9d9f45968c7915b277b29a9662\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4606d9d9f45968c7915b277b29a9662\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a40bf90101796a14a05da6bf9d6b8fe\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a40bf90101796a14a05da6bf9d6b8fe\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16b71b3088e9666a0519ca03cd7fb2af\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16b71b3088e9666a0519ca03cd7fb2af\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ff4138f34d916ab64efc1ad9ff9bbe4\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ff4138f34d916ab64efc1ad9ff9bbe4\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ffdefbe13277925eff2fd0eb5e23821\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ffdefbe13277925eff2fd0eb5e23821\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a47a47129b71dadb07682124089ee59\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a47a47129b71dadb07682124089ee59\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da9ca9f265ea25a5c32e964881d59f17\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da9ca9f265ea25a5c32e964881d59f17\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ac865f2e376b0e35b433497d52ff6a4\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ac865f2e376b0e35b433497d52ff6a4\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\066a0ad3a2dc6f9cdea2e59ec7063442\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\066a0ad3a2dc6f9cdea2e59ec7063442\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbbb40529799f840f5efdd34f185d49\transformed\ui-test-manifest-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbbb40529799f840f5efdd34f185d49\transformed\ui-test-manifest-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f6650a0e991f193d9beef7fe5f16423\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f6650a0e991f193d9beef7fe5f16423\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f93d72572c8f1eb0463681a76a9f82c\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f93d72572c8f1eb0463681a76a9f82c\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f20881ad2ecbf951bde9e37fb1a34d4\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f20881ad2ecbf951bde9e37fb1a34d4\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adf2c11f76fef535c1060fff21c61aed\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adf2c11f76fef535c1060fff21c61aed\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7de5344c12143cca11a0ab5e318bcc73\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7de5344c12143cca11a0ab5e318bcc73\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8b76eb325d7efbddd4cb92c49c77079\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8b76eb325d7efbddd4cb92c49c77079\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a09d6bd8eaa5be3f4376ce9ed7cf1b1\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a09d6bd8eaa5be3f4376ce9ed7cf1b1\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f60cddd10e3adb78377e455819e0e7db\transformed\navigation-runtime-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-runtime-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f60cddd10e3adb78377e455819e0e7db\transformed\navigation-runtime-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e9e2d1cf43cde8a32196cdda01a6068\transformed\navigation-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e9e2d1cf43cde8a32196cdda01a6068\transformed\navigation-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46ad2304e64c5a702977fad6696ea04d\transformed\navigation-common-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46ad2304e64c5a702977fad6696ea04d\transformed\navigation-common-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common:2.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd203b27384ed6abbecfa6eb1dbc818e\transformed\navigation-common-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common:2.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd203b27384ed6abbecfa6eb1dbc818e\transformed\navigation-common-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47c174996be18f1bb84fe8d589527704\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47c174996be18f1bb84fe8d589527704\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62e51bf216bfe5af090a1433c05af959\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62e51bf216bfe5af090a1433c05af959\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67663ff03f7437bfd40a417d66c88a5b\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67663ff03f7437bfd40a417d66c88a5b\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\058afe60a62462f8a077b2f93aad827c\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\058afe60a62462f8a077b2f93aad827c\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b9074ea43515050688821caed3e085a\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b9074ea43515050688821caed3e085a\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fdc6090eb890ea60e9895ac2d90c5f54\transformed\lifecycle-runtime-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fdc6090eb890ea60e9895ac2d90c5f54\transformed\lifecycle-runtime-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\755b1088b89d0e690888cbb7a34e5b94\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\755b1088b89d0e690888cbb7a34e5b94\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6df12ac5967402e13f4181ddc3c39571\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6df12ac5967402e13f4181ddc3c39571\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\553fdf91606a07a84412c88833cf4ae2\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\553fdf91606a07a84412c88833cf4ae2\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\758d06e28d876accf8cd86f1d399c64f\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\758d06e28d876accf8cd86f1d399c64f\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03425cb92fd71ca562259853bf49cb47\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03425cb92fd71ca562259853bf49cb47\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98d0104178181e4d53999be75c6c66be\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98d0104178181e4d53999be75c6c66be\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ac9d68b92fe2ab1028cb5e8476ae3bd\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ac9d68b92fe2ab1028cb5e8476ae3bd\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ee1bd6a009846944d541900d2397703\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ee1bd6a009846944d541900d2397703\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d8da8d98afa6f240c0f54189b1a77c0\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d8da8d98afa6f240c0f54189b1a77c0\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f8512599b3d55e93013e5ec39d4c7\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f8512599b3d55e93013e5ec39d4c7\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\736535d916a2f202cc5de9983b95bb18\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\736535d916a2f202cc5de9983b95bb18\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f0600dfc23bb70309fa0471f8b174b8\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f0600dfc23bb70309fa0471f8b174b8\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f7d0d13b3d445bb9dc84bbc73ccbf0\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f7d0d13b3d445bb9dc84bbc73ccbf0\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\38c461e1a459c353e8424d45ad6935e3\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\38c461e1a459c353e8424d45ad6935e3\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82f47d1133a0d23573d29136b38f5ad5\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82f47d1133a0d23573d29136b38f5ad5\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2366fe92f73a3c88d21f4742a2b39a90\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2366fe92f73a3c88d21f4742a2b39a90\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bbf81b3fa31fd696ace58d247b1e5d5\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bbf81b3fa31fd696ace58d247b1e5d5\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f69793395e8c81844fe849a95e93ba9\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f69793395e8c81844fe849a95e93ba9\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a5fc50ae51481312102ab347d031230\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a5fc50ae51481312102ab347d031230\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e6074fcf09995d920a59ab173fea8ae\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e6074fcf09995d920a59ab173fea8ae\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a869f15ba42402e929d2b68c92b9e30\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a869f15ba42402e929d2b68c92b9e30\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32c7557b32a33e18709389bce01f0583\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32c7557b32a33e18709389bce01f0583\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bc77b08baa5e058442f01d3b8740064\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bc77b08baa5e058442f01d3b8740064\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d88d238a2bf956356ce0d4cd266b5363\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d88d238a2bf956356ce0d4cd266b5363\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.dagger:dagger-lint-aar:2.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\93876383fd2f0373b1a38e7e8869f4f8\transformed\dagger-lint-aar-2.49\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\93876383fd2f0373b1a38e7e8869f4f8\transformed\dagger-lint-aar-2.49\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\abonelik_sistemi_final\app\src\main\AndroidManifest.xml
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44e19e1f9b56976b7f9e33316bf0ba7e\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44e19e1f9b56976b7f9e33316bf0ba7e\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44e19e1f9b56976b7f9e33316bf0ba7e\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44e19e1f9b56976b7f9e33316bf0ba7e\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f17758470c37236a8702f22be0817404\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f17758470c37236a8702f22be0817404\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f17758470c37236a8702f22be0817404\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:22-74
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ac865f2e376b0e35b433497d52ff6a4\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ac865f2e376b0e35b433497d52ff6a4\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ac865f2e376b0e35b433497d52ff6a4\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbbb40529799f840f5efdd34f185d49\transformed\ui-test-manifest-1.6.2\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbbb40529799f840f5efdd34f185d49\transformed\ui-test-manifest-1.6.2\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbbb40529799f840f5efdd34f185d49\transformed\ui-test-manifest-1.6.2\AndroidManifest.xml:24:13-63
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f93d72572c8f1eb0463681a76a9f82c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f93d72572c8f1eb0463681a76a9f82c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f93d72572c8f1eb0463681a76a9f82c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f20881ad2ecbf951bde9e37fb1a34d4\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f20881ad2ecbf951bde9e37fb1a34d4\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f20881ad2ecbf951bde9e37fb1a34d4\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.example.abonekaptanmobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.abonekaptanmobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f7d0d13b3d445bb9dc84bbc73ccbf0\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f7d0d13b3d445bb9dc84bbc73ccbf0\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f7d0d13b3d445bb9dc84bbc73ccbf0\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f7d0d13b3d445bb9dc84bbc73ccbf0\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f7d0d13b3d445bb9dc84bbc73ccbf0\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
