plugins {
    id 'com.android.application' version '8.8.0' apply false // Check for latest version
    id 'org.jetbrains.kotlin.android' version '1.9.21' apply false // Check for latest version
    id 'com.google.dagger.hilt.android' version '2.48.1' apply false // Check for latest version
    id 'com.google.gms.google-services' version '4.4.1' apply false // Check for latest version
    id 'org.jetbrains.kotlin.kapt' version '1.9.21' apply false // For Room/Hilt annotation processing if not using KSP
}