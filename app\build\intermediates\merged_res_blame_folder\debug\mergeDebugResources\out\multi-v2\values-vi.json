{"logs": [{"outputFile": "com.example.abonekaptanmobile.app-mergeDebugResources-67:/values-vi/values-vi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1062e727b039c203a5f49af9a364889\\transformed\\material3-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,289,396,513,614,709,821,958,1078,1219,1303,1406,1495,1591,1710,1833,1941,2068,2191,2318,2477,2604,2727,2847,2966,3056,3156,3274,3407,3502,3608,3715,3838,3968,4076,4172,4251,4348,4444,4533,4617,4724,4804,4887,4986,5084,5179,5278,5364,5465,5563,5665,5781,5861,5970", "endColumns": "118,114,106,116,100,94,111,136,119,140,83,102,88,95,118,122,107,126,122,126,158,126,122,119,118,89,99,117,132,94,105,106,122,129,107,95,78,96,95,88,83,106,79,82,98,97,94,98,85,100,97,101,115,79,108,103", "endOffsets": "169,284,391,508,609,704,816,953,1073,1214,1298,1401,1490,1586,1705,1828,1936,2063,2186,2313,2472,2599,2722,2842,2961,3051,3151,3269,3402,3497,3603,3710,3833,3963,4071,4167,4246,4343,4439,4528,4612,4719,4799,4882,4981,5079,5174,5273,5359,5460,5558,5660,5776,5856,5965,6069"}, "to": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3727,3846,3961,4068,4185,4286,4381,4493,4630,4750,4891,4975,5078,5167,5263,5382,5505,5613,5740,5863,5990,6149,6276,6399,6519,6638,6728,6828,6946,7079,7174,7280,7387,7510,7640,7748,7844,7923,8020,8116,8205,8289,8396,8476,8559,8658,8756,8851,8950,9036,9137,9235,9337,9453,9533,9642", "endColumns": "118,114,106,116,100,94,111,136,119,140,83,102,88,95,118,122,107,126,122,126,158,126,122,119,118,89,99,117,132,94,105,106,122,129,107,95,78,96,95,88,83,106,79,82,98,97,94,98,85,100,97,101,115,79,108,103", "endOffsets": "3841,3956,4063,4180,4281,4376,4488,4625,4745,4886,4970,5073,5162,5258,5377,5500,5608,5735,5858,5985,6144,6271,6394,6514,6633,6723,6823,6941,7074,7169,7275,7382,7505,7635,7743,7839,7918,8015,8111,8200,8284,8391,8471,8554,8653,8751,8846,8945,9031,9132,9230,9332,9448,9528,9637,9741"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\44e19e1f9b56976b7f9e33316bf0ba7e\\transformed\\play-services-base-18.0.1\\res\\values-vi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,449,570,675,836,962,1077,1177,1346,1449,1602,1728,1883,2028,2092,2152", "endColumns": "97,157,120,104,160,125,114,99,168,102,152,125,154,144,63,59,78", "endOffsets": "290,448,569,674,835,961,1076,1176,1345,1448,1601,1727,1882,2027,2091,2151,2230"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1017,1119,1281,1406,1515,1680,1810,1929,2161,2334,2441,2598,2728,2887,3036,3104,3168", "endColumns": "101,161,124,108,164,129,118,103,172,106,156,129,158,148,67,63,82", "endOffsets": "1114,1276,1401,1510,1675,1805,1924,2028,2329,2436,2593,2723,2882,3031,3099,3163,3246"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0ddf931afad622e529ec9d528ae33174\\transformed\\core-1.12.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "2,3,4,5,6,7,8,97", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,403,503,606,719,10314", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "197,299,398,498,601,714,830,10410"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f17758470c37236a8702f22be0817404\\transformed\\play-services-basement-18.2.0\\res\\values-vi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "123", "endOffsets": "318"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "2033", "endColumns": "127", "endOffsets": "2156"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ae9863232b9716e1e6ec45d64efe02de\\transformed\\foundation-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,86", "endOffsets": "138,225"}, "to": {"startLines": "101,102", "startColumns": "4,4", "startOffsets": "10672,10760", "endColumns": "87,86", "endOffsets": "10755,10842"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\758d06e28d876accf8cd86f1d399c64f\\transformed\\ui-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,287,393,493,585,670,763,857,938,1008,1078,1168,1259,1331,1408,1474", "endColumns": "95,85,105,99,91,84,92,93,80,69,69,89,90,71,76,65,113", "endOffsets": "196,282,388,488,580,665,758,852,933,1003,1073,1163,1254,1326,1403,1469,1583"}, "to": {"startLines": "9,10,29,30,31,32,33,90,91,92,93,94,95,96,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "835,931,3251,3357,3457,3549,3634,9746,9840,9921,9991,10061,10151,10242,10415,10492,10558", "endColumns": "95,85,105,99,91,84,92,93,80,69,69,89,90,71,76,65,113", "endOffsets": "926,1012,3352,3452,3544,3629,3722,9835,9916,9986,10056,10146,10237,10309,10487,10553,10667"}}]}]}