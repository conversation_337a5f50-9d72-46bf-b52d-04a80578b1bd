// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.data.repository;

import com.example.abonekaptanmobile.data.local.dao.CommunityPatternDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CommunityPatternRepository_Factory implements Factory<CommunityPatternRepository> {
  private final Provider<CommunityPatternDao> communityPatternDaoProvider;

  public CommunityPatternRepository_Factory(
      Provider<CommunityPatternDao> communityPatternDaoProvider) {
    this.communityPatternDaoProvider = communityPatternDaoProvider;
  }

  @Override
  public CommunityPatternRepository get() {
    return newInstance(communityPatternDaoProvider.get());
  }

  public static CommunityPatternRepository_Factory create(
      Provider<CommunityPatternDao> communityPatternDaoProvider) {
    return new CommunityPatternRepository_Factory(communityPatternDaoProvider);
  }

  public static CommunityPatternRepository newInstance(CommunityPatternDao communityPatternDao) {
    return new CommunityPatternRepository(communityPatternDao);
  }
}
