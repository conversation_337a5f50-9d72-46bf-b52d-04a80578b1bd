<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\abonelik_sistemi_final\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\abonelik_sistemi_final\app\src\main\res"><file name="ic_launcher_background" path="D:\abonelik_sistemi_final\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\abonelik_sistemi_final\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="D:\abonelik_sistemi_final\app\src\main\res\mipmap-anydpi\ic_launcher.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\abonelik_sistemi_final\app\src\main\res\mipmap-anydpi\ic_launcher_round.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\abonelik_sistemi_final\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\abonelik_sistemi_final\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\abonelik_sistemi_final\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\abonelik_sistemi_final\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\abonelik_sistemi_final\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\abonelik_sistemi_final\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\abonelik_sistemi_final\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\abonelik_sistemi_final\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\abonelik_sistemi_final\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\abonelik_sistemi_final\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\abonelik_sistemi_final\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="D:\abonelik_sistemi_final\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">AboneKaptan Mobile</string><string name="sign_in_prompt">Aboneliklerinizi yönetmek için Google ile oturum açın.</string><string name="sign_in_with_google">Google ile Oturum Aç</string><string name="sign_in_button">Oturum Aç Butonu</string><string name="refresh_subscriptions">Abonelikleri Yenile</string><string name="sign_out">Oturumu Kapat</string><string name="active_subscriptions">Aktif Abonelikler</string><string name="forgotten_subscriptions">Unutulmuş (İnaktif) Abonelikler</string><string name="cancelled_subscriptions">İptal Edilmiş Abonelikler</string><string name="unknown_subscriptions">Bilinmeyen Durum</string><string name="email_count">E-posta Sayısı</string><string name="last_email_date">Son E-posta Tarihi</string><string name="cancellation_date">İptal Tarihi</string><string name="is_this_wrong">Bu yanlış mı?</string><string name="feedback_dialog_title">%1$s için geri bildirim</string><string name="current_status_is">Mevcut durum: %1$s</string><string name="why_is_this_wrong">Neden yanlış olduğunu düşünüyorsunuz?</string><string name="feedback_option_is_subscription_active">Bu bir abonelik ve AKTİF</string><string name="feedback_option_is_subscription_forgotten">Bu bir abonelik ve UNUTULMUŞ</string><string name="feedback_option_is_cancelled">Bu abonelik İPTAL EDİLMİŞ</string><string name="feedback_option_not_a_subscription">Bu bir abonelik DEĞİL</string><string name="feedback_option_other">Diğer (Açıklayın)</string><string name="feedback_note_other">Notunuz (isteğe bağlı)</string><string name="submit_feedback">Gönder</string><string name="cancel">İptal</string><string name="no_subscriptions_found">Abonelik bulunamadı veya yüklenemedi. Lütfen yenileyin veya daha sonra tekrar deneyin.</string><string name="error_loading_subscriptions">Abonelikler yüklenirken hata oluştu.</string><string name="feedback_submitted">Geri bildiriminiz gönderildi.</string></file><file path="D:\abonelik_sistemi_final\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.AboneKaptanMobile" parent="android:Theme.Material.Light.NoActionBar"/></file><file name="backup_rules" path="D:\abonelik_sistemi_final\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\abonelik_sistemi_final\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="network_security_config" path="D:\abonelik_sistemi_final\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\abonelik_sistemi_final\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\abonelik_sistemi_final\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\abonelik_sistemi_final\app\build\generated\res\resValues\debug"/><source path="D:\abonelik_sistemi_final\app\build\generated\res\processDebugGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\abonelik_sistemi_final\app\build\generated\res\resValues\debug"/><source path="D:\abonelik_sistemi_final\app\build\generated\res\processDebugGoogleServices"><file path="D:\abonelik_sistemi_final\app\build\generated\res\processDebugGoogleServices\values\values.xml" qualifiers=""><string name="gcm_defaultSenderId" translatable="false">461703166146</string><string name="google_api_key" translatable="false">AIzaSyC1oQgESD24sVdB48Klore1ZHiRRftwf_M</string><string name="google_app_id" translatable="false">1:461703166146:android:eb621ff6952c4a7684e35b</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyC1oQgESD24sVdB48Klore1ZHiRRftwf_M</string><string name="google_storage_bucket" translatable="false">abonekaptanmobileproject.firebasestorage.app</string><string name="project_id" translatable="false">abonekaptanmobileproject</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>