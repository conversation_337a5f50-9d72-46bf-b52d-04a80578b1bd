package com.example.abonekaptanmobile.model;

import com.example.abonekaptanmobile.data.remote.model.ClassificationResult;

/**
 * Turkish: E-posta türlerini temsil eden enum.
 * English: Enum representing email types.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\n\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\n\u00a8\u0006\u000b"}, d2 = {"Lcom/example/abonekaptanmobile/model/EmailType;", "", "(Ljava/lang/String;I)V", "SUBSCRIPTION_START", "SUBSCRIPTION_CANCEL", "SUBSCRIPTION_RENEWAL", "PAYMENT_CONFIRMATION", "WELCOME_MESSAGE", "PROMOTIONAL_MESSAGE", "GENERAL_NOTIFICATION", "UNKNOWN", "app_debug"})
public enum EmailType {
    /*public static final*/ SUBSCRIPTION_START /* = new SUBSCRIPTION_START() */,
    /*public static final*/ SUBSCRIPTION_CANCEL /* = new SUBSCRIPTION_CANCEL() */,
    /*public static final*/ SUBSCRIPTION_RENEWAL /* = new SUBSCRIPTION_RENEWAL() */,
    /*public static final*/ PAYMENT_CONFIRMATION /* = new PAYMENT_CONFIRMATION() */,
    /*public static final*/ WELCOME_MESSAGE /* = new WELCOME_MESSAGE() */,
    /*public static final*/ PROMOTIONAL_MESSAGE /* = new PROMOTIONAL_MESSAGE() */,
    /*public static final*/ GENERAL_NOTIFICATION /* = new GENERAL_NOTIFICATION() */,
    /*public static final*/ UNKNOWN /* = new UNKNOWN() */;
    
    EmailType() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.example.abonekaptanmobile.model.EmailType> getEntries() {
        return null;
    }
}