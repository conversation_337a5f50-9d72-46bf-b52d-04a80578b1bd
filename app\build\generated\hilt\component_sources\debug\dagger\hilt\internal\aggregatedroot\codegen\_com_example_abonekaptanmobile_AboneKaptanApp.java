package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.example.abonekaptanmobile.AboneKaptanApp",
    rootPackage = "com.example.abonekaptanmobile",
    originatingRoot = "com.example.abonekaptanmobile.AboneKaptanApp",
    originatingRootPackage = "com.example.abonekaptanmobile",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "AboneKaptanApp",
    originatingRootSimpleNames = "AboneKaptanApp"
)
public class _com_example_abonekaptanmobile_AboneKaptanApp {
}
