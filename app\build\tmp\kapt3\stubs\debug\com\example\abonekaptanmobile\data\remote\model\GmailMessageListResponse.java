package com.example.abonekaptanmobile.data.remote.model;

import com.google.gson.annotations.SerializedName;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001B\'\u0012\u000e\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003\u0012\b\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\u0006\u0010\u0007\u001a\u00020\b\u00a2\u0006\u0002\u0010\tJ\u0011\u0010\u0010\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\bH\u00c6\u0003J1\u0010\u0013\u001a\u00020\u00002\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\b\b\u0002\u0010\u0007\u001a\u00020\bH\u00c6\u0001J\u0013\u0010\u0014\u001a\u00020\u00152\b\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0017\u001a\u00020\bH\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0006H\u00d6\u0001R\u001e\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0018\u0010\u0005\u001a\u0004\u0018\u00010\u00068\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0016\u0010\u0007\u001a\u00020\b8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000f\u00a8\u0006\u0019"}, d2 = {"Lcom/example/abonekaptanmobile/data/remote/model/GmailMessageListResponse;", "", "messages", "", "Lcom/example/abonekaptanmobile/data/remote/model/MessageId;", "nextPageToken", "", "resultSizeEstimate", "", "(Ljava/util/List;Ljava/lang/String;I)V", "getMessages", "()Ljava/util/List;", "getNextPageToken", "()Ljava/lang/String;", "getResultSizeEstimate", "()I", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "toString", "app_debug"})
public final class GmailMessageListResponse {
    @com.google.gson.annotations.SerializedName(value = "messages")
    @org.jetbrains.annotations.Nullable()
    private final java.util.List<com.example.abonekaptanmobile.data.remote.model.MessageId> messages = null;
    @com.google.gson.annotations.SerializedName(value = "nextPageToken")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String nextPageToken = null;
    @com.google.gson.annotations.SerializedName(value = "resultSizeEstimate")
    private final int resultSizeEstimate = 0;
    
    public GmailMessageListResponse(@org.jetbrains.annotations.Nullable()
    java.util.List<com.example.abonekaptanmobile.data.remote.model.MessageId> messages, @org.jetbrains.annotations.Nullable()
    java.lang.String nextPageToken, int resultSizeEstimate) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.example.abonekaptanmobile.data.remote.model.MessageId> getMessages() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getNextPageToken() {
        return null;
    }
    
    public final int getResultSizeEstimate() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.example.abonekaptanmobile.data.remote.model.MessageId> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component2() {
        return null;
    }
    
    public final int component3() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.data.remote.model.GmailMessageListResponse copy(@org.jetbrains.annotations.Nullable()
    java.util.List<com.example.abonekaptanmobile.data.remote.model.MessageId> messages, @org.jetbrains.annotations.Nullable()
    java.lang.String nextPageToken, int resultSizeEstimate) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}