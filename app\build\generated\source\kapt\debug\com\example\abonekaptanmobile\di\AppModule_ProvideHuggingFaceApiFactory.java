// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.di;

import com.example.abonekaptanmobile.data.remote.HuggingFaceApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;
import okhttp3.OkHttpClient;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideHuggingFaceApiFactory implements Factory<HuggingFaceApi> {
  private final Provider<OkHttpClient> okHttpClientProvider;

  public AppModule_ProvideHuggingFaceApiFactory(Provider<OkHttpClient> okHttpClientProvider) {
    this.okHttpClientProvider = okHttpClientProvider;
  }

  @Override
  public HuggingFaceApi get() {
    return provideHuggingFaceApi(okHttpClientProvider.get());
  }

  public static AppModule_ProvideHuggingFaceApiFactory create(
      Provider<OkHttpClient> okHttpClientProvider) {
    return new AppModule_ProvideHuggingFaceApiFactory(okHttpClientProvider);
  }

  public static HuggingFaceApi provideHuggingFaceApi(OkHttpClient okHttpClient) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideHuggingFaceApi(okHttpClient));
  }
}
