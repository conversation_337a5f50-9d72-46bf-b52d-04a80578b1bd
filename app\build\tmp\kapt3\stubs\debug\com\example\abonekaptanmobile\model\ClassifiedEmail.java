package com.example.abonekaptanmobile.model;

import com.example.abonekaptanmobile.data.remote.model.ClassificationResult;

/**
 * Turkish: Sınıflandırılmış bir e-postayı temsil eder.
 * English: Represents a classified email.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u001d\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001BW\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t\u0012\b\b\u0002\u0010\n\u001a\u00020\u000b\u0012\b\b\u0002\u0010\f\u001a\u00020\r\u0012\u000e\b\u0002\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000f\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\u0012J\t\u0010!\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\"\u001a\u00020\u0005H\u00c6\u0003J\t\u0010#\u001a\u00020\u0007H\u00c6\u0003J\u0010\u0010$\u001a\u0004\u0018\u00010\tH\u00c6\u0003\u00a2\u0006\u0002\u0010\u001bJ\t\u0010%\u001a\u00020\u000bH\u00c6\u0003J\t\u0010&\u001a\u00020\rH\u00c6\u0003J\u000f\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u00100\u000fH\u00c6\u0003J\t\u0010(\u001a\u00020\u0007H\u00c6\u0003Jf\u0010)\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t2\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\r2\u000e\b\u0002\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000f2\b\b\u0002\u0010\u0011\u001a\u00020\u0007H\u00c6\u0001\u00a2\u0006\u0002\u0010*J\u0013\u0010+\u001a\u00020\u00072\b\u0010,\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010-\u001a\u00020.H\u00d6\u0001J\t\u0010/\u001a\u00020\u0005H\u00d6\u0001R\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0019R\u0011\u0010\u0011\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0019R\u0015\u0010\b\u001a\u0004\u0018\u00010\t\u00a2\u0006\n\n\u0002\u0010\u001c\u001a\u0004\b\u001a\u0010\u001bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001eR\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010 \u00a8\u00060"}, d2 = {"Lcom/example/abonekaptanmobile/model/ClassifiedEmail;", "", "rawEmail", "Lcom/example/abonekaptanmobile/model/RawEmail;", "identifiedService", "", "isLikelySubscription", "", "matchedPatternId", "", "subscriptionType", "Lcom/example/abonekaptanmobile/model/SubscriptionType;", "emailType", "Lcom/example/abonekaptanmobile/model/EmailType;", "classificationResults", "", "Lcom/example/abonekaptanmobile/data/remote/model/ClassificationResult;", "isPaidSubscription", "(Lcom/example/abonekaptanmobile/model/RawEmail;Ljava/lang/String;ZLjava/lang/Long;Lcom/example/abonekaptanmobile/model/SubscriptionType;Lcom/example/abonekaptanmobile/model/EmailType;Ljava/util/List;Z)V", "getClassificationResults", "()Ljava/util/List;", "getEmailType", "()Lcom/example/abonekaptanmobile/model/EmailType;", "getIdentifiedService", "()Ljava/lang/String;", "()Z", "getMatchedPatternId", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getRawEmail", "()Lcom/example/abonekaptanmobile/model/RawEmail;", "getSubscriptionType", "()Lcom/example/abonekaptanmobile/model/SubscriptionType;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "(Lcom/example/abonekaptanmobile/model/RawEmail;Ljava/lang/String;ZLjava/lang/Long;Lcom/example/abonekaptanmobile/model/SubscriptionType;Lcom/example/abonekaptanmobile/model/EmailType;Ljava/util/List;Z)Lcom/example/abonekaptanmobile/model/ClassifiedEmail;", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class ClassifiedEmail {
    @org.jetbrains.annotations.NotNull()
    private final com.example.abonekaptanmobile.model.RawEmail rawEmail = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String identifiedService = null;
    private final boolean isLikelySubscription = false;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Long matchedPatternId = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.abonekaptanmobile.model.SubscriptionType subscriptionType = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.abonekaptanmobile.model.EmailType emailType = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.abonekaptanmobile.data.remote.model.ClassificationResult> classificationResults = null;
    private final boolean isPaidSubscription = false;
    
    public ClassifiedEmail(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.model.RawEmail rawEmail, @org.jetbrains.annotations.NotNull()
    java.lang.String identifiedService, boolean isLikelySubscription, @org.jetbrains.annotations.Nullable()
    java.lang.Long matchedPatternId, @org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.model.SubscriptionType subscriptionType, @org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.model.EmailType emailType, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.abonekaptanmobile.data.remote.model.ClassificationResult> classificationResults, boolean isPaidSubscription) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.model.RawEmail getRawEmail() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getIdentifiedService() {
        return null;
    }
    
    public final boolean isLikelySubscription() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long getMatchedPatternId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.model.SubscriptionType getSubscriptionType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.model.EmailType getEmailType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.abonekaptanmobile.data.remote.model.ClassificationResult> getClassificationResults() {
        return null;
    }
    
    public final boolean isPaidSubscription() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.model.RawEmail component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    public final boolean component3() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.model.SubscriptionType component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.model.EmailType component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.abonekaptanmobile.data.remote.model.ClassificationResult> component7() {
        return null;
    }
    
    public final boolean component8() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.model.ClassifiedEmail copy(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.model.RawEmail rawEmail, @org.jetbrains.annotations.NotNull()
    java.lang.String identifiedService, boolean isLikelySubscription, @org.jetbrains.annotations.Nullable()
    java.lang.Long matchedPatternId, @org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.model.SubscriptionType subscriptionType, @org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.model.EmailType emailType, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.abonekaptanmobile.data.remote.model.ClassificationResult> classificationResults, boolean isPaidSubscription) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}