// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.di;

import com.example.abonekaptanmobile.data.local.dao.FeedbackDao;
import com.example.abonekaptanmobile.data.repository.FeedbackRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideFeedbackRepositoryFactory implements Factory<FeedbackRepository> {
  private final Provider<FeedbackDao> daoProvider;

  public AppModule_ProvideFeedbackRepositoryFactory(Provider<FeedbackDao> daoProvider) {
    this.daoProvider = daoProvider;
  }

  @Override
  public FeedbackRepository get() {
    return provideFeedbackRepository(daoProvider.get());
  }

  public static AppModule_ProvideFeedbackRepositoryFactory create(
      Provider<FeedbackDao> daoProvider) {
    return new AppModule_ProvideFeedbackRepositoryFactory(daoProvider);
  }

  public static FeedbackRepository provideFeedbackRepository(FeedbackDao dao) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideFeedbackRepository(dao));
  }
}
