  
AndroidJUnit4 androidx.test.ext.junit.runners  InstrumentationRegistry androidx.test.platform.app  
AndroidJUnit4 com.example.abonekaptanmobile  ExampleInstrumentedTest com.example.abonekaptanmobile  Test 5com.example.abonekaptanmobile.ExampleInstrumentedTest  
AndroidJUnit4 	java.lang  
AndroidJUnit4 kotlin  
AndroidJUnit4 kotlin.annotation  
AndroidJUnit4 kotlin.collections  
AndroidJUnit4 kotlin.comparisons  
AndroidJUnit4 	kotlin.io  
AndroidJUnit4 
kotlin.jvm  
AndroidJUnit4 
kotlin.ranges  KClass kotlin.reflect  
AndroidJUnit4 kotlin.sequences  
AndroidJUnit4 kotlin.text  Assert 	org.junit  Test 	org.junit  
AndroidJUnit4 org.junit.Assert  RunWith org.junit.runner                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   