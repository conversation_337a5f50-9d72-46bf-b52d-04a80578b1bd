// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.di;

import android.content.Context;
import com.example.abonekaptanmobile.auth.GoogleAuthManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideGoogleAuthManagerFactory implements Factory<GoogleAuthManager> {
  private final Provider<Context> contextProvider;

  public AppModule_ProvideGoogleAuthManagerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public GoogleAuthManager get() {
    return provideGoogleAuthManager(contextProvider.get());
  }

  public static AppModule_ProvideGoogleAuthManagerFactory create(
      Provider<Context> contextProvider) {
    return new AppModule_ProvideGoogleAuthManagerFactory(contextProvider);
  }

  public static GoogleAuthManager provideGoogleAuthManager(Context context) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideGoogleAuthManager(context));
  }
}
