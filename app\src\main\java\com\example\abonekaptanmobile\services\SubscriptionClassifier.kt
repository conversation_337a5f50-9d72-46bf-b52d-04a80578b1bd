// file: app/java/com/example/abonekaptanmobile/services/SubscriptionClassifier.kt
package com.example.abonekaptanmobile.services

import android.util.Log
import com.example.abonekaptanmobile.data.local.entity.PatternType
import com.example.abonekaptanmobile.data.local.entity.SubscriptionPatternEntity
import com.example.abonekaptanmobile.data.remote.model.ClassificationResult
import com.example.abonekaptanmobile.data.repository.CommunityPatternRepository
import com.example.abonekaptanmobile.data.repository.HuggingFaceRepository
import com.example.abonekaptanmobile.data.remote.model.TwoStageAnalysisResult
import com.example.abonekaptanmobile.data.remote.model.FinalSubscriptionStatus
import com.example.abonekaptanmobile.model.*
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import java.util.concurrent.TimeUnit
import java.util.regex.PatternSyntaxException
import javax.inject.Inject

/**
 * Turkish: E-postaları abonelik durumuna göre sınıflandıran servis.
 * English: Service that classifies emails based on subscription status.
 */
class SubscriptionClassifier @Inject constructor(
    private val communityPatternRepo: CommunityPatternRepository,
    private val huggingFaceRepository: HuggingFaceRepository
) {
    private val inactivityThresholdDays = 90L
    private val inactivityThresholdMillis = TimeUnit.DAYS.toMillis(inactivityThresholdDays)

    // İptal kalıpları - Geliştirilmiş
    private val cancelPatterns = listOf(
        Regex("aboneliğiniz iptal edildi", RegexOption.IGNORE_CASE),
        Regex("üyeliğiniz sonlandırıldı", RegexOption.IGNORE_CASE),
        Regex("subscription cancelled", RegexOption.IGNORE_CASE),
        Regex("membership ended", RegexOption.IGNORE_CASE),
        Regex("your subscription has been canceled", RegexOption.IGNORE_CASE),
        Regex("hesabınız kapatıldı", RegexOption.IGNORE_CASE),
        Regex("account closed", RegexOption.IGNORE_CASE),
        Regex("iptal onaylandı", RegexOption.IGNORE_CASE),
        Regex("cancellation confirmed", RegexOption.IGNORE_CASE),
        Regex("üyeliğiniz sona erdi", RegexOption.IGNORE_CASE),
        Regex("aboneliğiniz sona ermiştir", RegexOption.IGNORE_CASE),
        Regex("subscription has been terminated", RegexOption.IGNORE_CASE),
        Regex("membership has been cancelled", RegexOption.IGNORE_CASE),
        Regex("we've processed your cancellation", RegexOption.IGNORE_CASE),
        Regex("your cancellation request", RegexOption.IGNORE_CASE),
        Regex("iptal talebiniz", RegexOption.IGNORE_CASE),
        Regex("aboneliğinizi iptal ettiniz", RegexOption.IGNORE_CASE)
    )

    // Abonelik başlangıç kalıpları
    private val startPatterns = listOf(
        Regex("aboneliğiniz başladı", RegexOption.IGNORE_CASE),
        Regex("üyeliğiniz aktifleştirildi", RegexOption.IGNORE_CASE),
        Regex("subscription activated", RegexOption.IGNORE_CASE),
        Regex("welcome to your .* subscription", RegexOption.IGNORE_CASE),
        Regex("your subscription has started", RegexOption.IGNORE_CASE),
        Regex("your membership has begun", RegexOption.IGNORE_CASE),
        Regex("aboneliğiniz başarıyla oluşturuldu", RegexOption.IGNORE_CASE),
        Regex("üyeliğiniz başarıyla başlatıldı", RegexOption.IGNORE_CASE),
        Regex("subscription confirmed", RegexOption.IGNORE_CASE),
        Regex("thank you for subscribing", RegexOption.IGNORE_CASE),
        Regex("abone olduğunuz için teşekkürler", RegexOption.IGNORE_CASE),
        Regex("hoş geldiniz", RegexOption.IGNORE_CASE),
        Regex("welcome to", RegexOption.IGNORE_CASE)
    )

    // Ödeme kalıpları
    private val paymentPatterns = listOf(
        Regex("aylık ödeme planı", RegexOption.IGNORE_CASE),
        Regex("yıllık ödeme planı", RegexOption.IGNORE_CASE),
        Regex("monthly subscription payment", RegexOption.IGNORE_CASE),
        Regex("annual subscription payment", RegexOption.IGNORE_CASE),
        Regex("faturanız", RegexOption.IGNORE_CASE),
        Regex("makbuzunuz", RegexOption.IGNORE_CASE),
        Regex("üyelik ücreti", RegexOption.IGNORE_CASE),
        Regex("payment receipt", RegexOption.IGNORE_CASE),
        Regex("invoice for your subscription", RegexOption.IGNORE_CASE),
        Regex("payment confirmation", RegexOption.IGNORE_CASE),
        Regex("ödeme onayı", RegexOption.IGNORE_CASE),
        Regex("ödemeniz alındı", RegexOption.IGNORE_CASE),
        Regex("payment received", RegexOption.IGNORE_CASE),
        Regex("\\d+[.,]\\d{2} (TL|USD|EUR|GBP)", RegexOption.IGNORE_CASE),
        Regex("\\$\\d+[.,]\\d{2}", RegexOption.IGNORE_CASE),
        Regex("€\\d+[.,]\\d{2}", RegexOption.IGNORE_CASE),
        Regex("£\\d+[.,]\\d{2}", RegexOption.IGNORE_CASE),
        Regex("\\d+[.,]\\d{2} ₺", RegexOption.IGNORE_CASE)
    )

    // Reklam kalıpları
    private val promotionalPatterns = listOf(
        Regex("özel teklif", RegexOption.IGNORE_CASE),
        Regex("special offer", RegexOption.IGNORE_CASE),
        Regex("limited time offer", RegexOption.IGNORE_CASE),
        Regex("sınırlı süre teklifi", RegexOption.IGNORE_CASE),
        Regex("discount", RegexOption.IGNORE_CASE),
        Regex("indirim", RegexOption.IGNORE_CASE),
        Regex("kampanya", RegexOption.IGNORE_CASE),
        Regex("promotion", RegexOption.IGNORE_CASE),
        Regex("deal", RegexOption.IGNORE_CASE),
        Regex("fırsat", RegexOption.IGNORE_CASE),
        Regex("kaçırma", RegexOption.IGNORE_CASE),
        Regex("don't miss", RegexOption.IGNORE_CASE),
        Regex("ücretsiz deneme", RegexOption.IGNORE_CASE),
        Regex("free trial", RegexOption.IGNORE_CASE)
    )

    // Güvenilir abonelik şirketlerinin domain listesi - SADECE GERÇEK ABONELİK SERVİSLERİ
    private val trustedSubscriptionDomains = setOf(
        // Video Streaming servisleri
        "netflix.com", "spotify.com", "disney.com", "disneyplus.com", "hulu.com",
        "hbomax.com", "max.com", "paramount.com", "paramountplus.com", "peacocktv.com",
        "crunchyroll.com", "twitch.tv", "youtube.com", "vimeo.com", "starz.com",
        "espn.com", "dazn.com", "mubi.com", "jiocinema.com", "globoplay.globo.com",
        "viu.com", "discoveryplus.com", "zee5.com", "rtlplus.de", "ivi.ru", "shahid.net",

        // Türk streaming servisleri
        "puhutv.com", "blutv.com", "exxen.com", "gain.tv", "tivibu.com.tr",
        "digiturk.com.tr", "dsmart.com.tr", "beinsports.com.tr", "tvplus.com.tr",

        // Müzik servisleri
        "spotify.com", "music.apple.com", "music.amazon.com", "tidal.com",
        "deezer.com", "soundcloud.com", "audible.com",

        // Kitap ve içerik
        "kindle.amazon.com", "storytel.com", "medium.com", "substack.com",

        // Eğitim platformları
        "coursera.org", "udemy.com", "linkedin.com/learning", "masterclass.com",
        "codecademy.com", "udacity.com", "skillshare.com", "duolingo.com",
        "babbel.com", "rosettastone.com", "brilliant.org", "pluralsight.com",

        // Sağlık ve fitness
        "peloton.com", "classpass.com", "fitness.apple.com", "fitbit.com",
        "calm.com", "headspace.com", "weightwatchers.com", "noom.com",
        "tonal.com", "mirror.co", "zwift.com", "betterhelp.com", "talkspace.com",
        "strava.com",

        // Finans ve haber
        "bloomberg.com", "wsj.com", "ft.com", "economist.com", "nytimes.com",
        "washingtonpost.com", "forbes.com", "barrons.com", "finance.yahoo.com",
        "seekingalpha.com", "morningstar.com", "reuters.com",

        // Oyun servisleri
        "xbox.com", "playstation.com", "nintendo.com", "ea.com", "arcade.apple.com",
        "ubisoft.com", "discord.com", "roblox.com",

        // Yazılım ve SaaS
        "microsoft.com", "office365.com", "workspace.google.com", "adobe.com",
        "salesforce.com", "aws.amazon.com", "azure.microsoft.com", "zoom.us",
        "slack.com", "atlassian.com", "dropbox.com", "box.com", "github.com",
        "gitlab.com", "canva.com", "asana.com", "trello.com", "docusign.com",
        "eventbrite.com", "grammarly.com", "webflow.com", "hubspot.com",
        "mailchimp.com", "surveymonkey.com", "zendesk.com", "servicenow.com",
        "twilio.com", "autodesk.com", "shopify.com",

        // Perakende üyelikler
        "amazon.com/prime", "costco.com", "samsclub.com",

        // Abonelik kutuları
        "hellofresh.com", "blueapron.com", "dollarshaveclub.com", "birchbox.com",
        "barkbox.com", "graze.com",

        // İçerik platformları
        "patreon.com", "onlyfans.com",

        // Productivity
        "evernote.com", "todoist.com", "notion.so", "monday.com",

        // Web hosting (sadece ücretli planlar)
        "squarespace.com", "wix.com", "wordpress.com", "godaddy.com",
        "bluehost.com", "hostgator.com", "siteground.com", "cloudflare.com",
        "digitalocean.com", "linode.com", "vultr.com", "heroku.com", "vercel.com",
        "netlify.com", "supabase.io", "planetscale.com",

        // Telekomünikasyon
        "turkcell.com.tr", "vodafone.com.tr", "turk-telekom.com.tr"

        // NOT: Google, Apple, Amazon (genel), LinkedIn, Facebook, Twitter, Instagram, TikTok
        // çıkarıldı çünkü bunlar çoğunlukla ücretsiz servisler ve bildirimler gönderiyorlar
    )

    /**
     * Turkish: E-postaları sınıflandırır ve abonelik öğelerini oluşturur.
     * English: Classifies emails and creates subscription items.
     */
    suspend fun classifyEmails(
        allRawEmails: List<RawEmail>,
        onProgress: ((progress: Float, status: String) -> Unit)? = null
    ): List<SubscriptionItem> = coroutineScope {
        Log.i("SubscriptionClassifier", "ClassifyEmails - Input: ${allRawEmails.size} emails.")

        // Progress tracking
        val totalEmails = allRawEmails.size
        val startTime = System.currentTimeMillis()

        onProgress?.invoke(0f, "E-postalar hazırlanıyor...")

        // E-postaları tarih sırasına göre sırala (en eskiden en yeniye - kronolojik sıra)
        val sortedEmails = allRawEmails.sortedBy { it.date }
        val classifiedDetailsCollector = mutableMapOf<String, MutableList<ClassifiedEmail>>()

        // Adım 1: Kesinlikle abonelik olmayanları ele
        val nonSubscriptionPatterns = communityPatternRepo.getNonSubscriptionPatterns().sortedByDescending { it.priority }
        Log.d("SubscriptionClassifier", "Fetched ${nonSubscriptionPatterns.size} non-subscription patterns. Examples: ${nonSubscriptionPatterns.take(3).joinToString { it.serviceName }}")

        // Adım 2: Tüm güvenilir abonelik kalıplarını çek ve önceliğe göre sırala
        val allReliableSubPatterns = communityPatternRepo.getReliableSubscriptionPatterns()
            .filter { it.isSubscription } // Sadece abonelik olanları al
            .sortedByDescending { it.priority }
        Log.i("SubscriptionClassifier", "Fetched ${allReliableSubPatterns.size} reliable subscription patterns (isSubscription=true, sorted by priority). Examples: ${allReliableSubPatterns.take(5).joinToString { p -> "${p.serviceName}(${p.priority}, ${p.source})" }}")

        if (allReliableSubPatterns.isEmpty()) {
            Log.w("SubscriptionClassifier", "No reliable subscription patterns found in the database! Seeding might have failed or patterns are not marked correctly.")
        }

        // Adım 3: HYBRID APPROACH - Geliştirilmiş AI sınıflandırması ile e-postaları işle
        onProgress?.invoke(0.1f, "E-postalar sınıflandırılıyor...")

        // Progress tracking için AtomicInteger kullan
        val processedEmailsAtomic = java.util.concurrent.atomic.AtomicInteger(0)

        val classificationJobs = sortedEmails.mapIndexed { index, email ->
            async {
                try {
                    // E-posta içeriğini hazırla
                    val emailContent = prepareEmailContentForClassification(email)

                    // Email domain'ini çıkar
                    val emailDomain = extractDomain(email.from) ?: "unknown"

                    // 🤖 GÜÇLÜ AI ANALİZİ - Manuel pattern'lar tamamen devre dışı
                    Log.v("SubscriptionClassifier", "🤖 AI Processing email from domain: $emailDomain")
                    Log.d("SubscriptionClassifier", "🤖 GÜÇLÜ AI ANALİZİ başlatılıyor - Email: ${email.subject.take(50)}")

                    // SADECE AI KULLANIYORUZ - Eski hybrid sistem tamamen devre dışı
                    val aiResult = performAdvancedAIAnalysis(email)
                    Log.d("SubscriptionClassifier", "🤖 AI SONUCU - Company: ${aiResult.company}, Type: ${aiResult.emailType}, Confidence: ${String.format("%.2f", aiResult.confidence)}")

                    // AI sonucunu direkt kullan - hybrid validation'a gerek yok
                    val serviceName = if (aiResult.confidence >= 0.6 && aiResult.company != "unknown") {
                        normalizeCompanyName(aiResult.company)
                    } else {
                        "unknown"
                    }

                    val emailType = convertHybridEmailTypeToEmailType(aiResult.emailType)
                    val isPaidSubscription = aiResult.confidence >= 0.6 && aiResult.company != "unknown"
                    val isLikelySubscription = aiResult.confidence >= 0.6 && aiResult.company != "unknown"

                    // AI güven skoruna göre abonelik tespiti
                    if (isLikelySubscription && serviceName != "unknown") {
                        val classifiedEmail = ClassifiedEmail(
                            rawEmail = email,
                            identifiedService = serviceName,
                            isLikelySubscription = isLikelySubscription,
                            matchedPatternId = null,
                            subscriptionType = if (isPaidSubscription) SubscriptionType.PAID else SubscriptionType.FREE,
                            emailType = emailType,
                            classificationResults = emptyList(),
                            isPaidSubscription = isPaidSubscription
                        )

                        synchronized(classifiedDetailsCollector) {
                            classifiedDetailsCollector.getOrPut(serviceName) { mutableListOf() }.add(classifiedEmail)
                        }

                        Log.i("SubscriptionClassifier", "✅ AI ABONELIK BULUNDU: ${email.subject.take(30)} for $serviceName, " +
                                "type: ${emailType.name}, isPaid: $isPaidSubscription, confidence: ${String.format("%.2f", aiResult.confidence)}")
                    } else {
                        Log.d("SubscriptionClassifier", "❌ AI abonelik değil: ${email.subject.take(30)}, " +
                                "company: ${aiResult.company}, emailType: ${aiResult.emailType}, confidence: ${String.format("%.2f", aiResult.confidence)}")
                    }



                    // Progress güncelleme - AtomicInteger kullanarak thread-safe
                    val currentProcessed = processedEmailsAtomic.incrementAndGet()
                    val progress = (currentProcessed.toFloat() / totalEmails) * 0.8f + 0.1f // %10-90 arası
                    val elapsedTime = System.currentTimeMillis() - startTime
                    val avgTimePerEmail = if (currentProcessed > 0) elapsedTime / currentProcessed else 0
                    val remainingEmails = totalEmails - currentProcessed
                    val estimatedRemainingTime = (remainingEmails * avgTimePerEmail) / 1000 // saniye

                    val status = "E-posta ${currentProcessed}/${totalEmails} işlendi" +
                                if (estimatedRemainingTime > 0) " (Tahmini ${estimatedRemainingTime.toInt()}s kaldı)" else ""

                    onProgress?.invoke(progress, status)
                } catch (e: Exception) {
                    Log.e("SubscriptionClassifier", "Error classifying email: ${e.message}", e)
                    val currentProcessed = processedEmailsAtomic.incrementAndGet()
                    val progress = (currentProcessed.toFloat() / totalEmails) * 0.8f + 0.1f
                    onProgress?.invoke(progress, "E-posta ${currentProcessed}/${totalEmails} işlendi (hata)")
                }
            }
        }

        // Tüm sınıflandırma işlemlerinin tamamlanmasını bekle
        classificationJobs.awaitAll()

        // Adım 4: Pattern matching sistemini tamamen devre dışı bırak
        Log.i("SubscriptionClassifier", "Pattern matching devre dışı - sadece domain filtreleme ve AI sınıflandırması kullanılıyor")

        onProgress?.invoke(0.95f, "Abonelik öğeleri oluşturuluyor...")

        Log.i("SubscriptionClassifier", "📊 TOPLAM TESPİT EDİLEN SERVİSLER: ${classifiedDetailsCollector.keys.size}")
        Log.i("SubscriptionClassifier", "🏢 Servisler: ${classifiedDetailsCollector.keys.joinToString(", ")}")

        // Her servis için detay bilgisi
        classifiedDetailsCollector.forEach { (serviceName, emails) ->
            Log.i("SubscriptionClassifier", "📧 $serviceName: ${emails.size} e-posta")
        }

        val result = createSubscriptionItemsFromDetails(classifiedDetailsCollector)

        Log.i("SubscriptionClassifier", "🎯 SONUÇ: ${result.size} abonelik oluşturuldu")
        result.forEach { subscription ->
            Log.i("SubscriptionClassifier", "✅ ${subscription.serviceName}: ${subscription.status.name} (${subscription.emailCount} e-posta)")
        }

        onProgress?.invoke(1.0f, "Tamamlandı! ${result.size} abonelik bulundu.")

        return@coroutineScope result
    }

    /**
     * Turkish: E-posta türünü belirler.
     * English: Determines the email type.
     */
    private fun determineEmailType(emailTypeResult: com.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult, email: RawEmail): EmailType {
        // Önce AI sonucuna göre belirle
        val primaryLabel = emailTypeResult.primaryLabel
        val primaryScore = emailTypeResult.primaryScore

        if (primaryScore > 0.7f) {
            return when (primaryLabel) {
                "paid_subscription_confirmation" -> EmailType.SUBSCRIPTION_START
                "subscription_welcome" -> EmailType.WELCOME_MESSAGE
                "subscription_cancellation" -> EmailType.SUBSCRIPTION_CANCEL
                "subscription_renewal" -> EmailType.SUBSCRIPTION_RENEWAL
                "payment_receipt" -> EmailType.PAYMENT_CONFIRMATION
                "promotional_offer" -> EmailType.PROMOTIONAL_MESSAGE
                "general_notification" -> EmailType.GENERAL_NOTIFICATION
                else -> determineEmailTypeFromPatterns(email)
            }
        }

        // AI sonucu yeterince güvenilir değilse, kalıplarla kontrol et
        return determineEmailTypeFromPatterns(email)
    }

    /**
     * Turkish: Kalıplara göre e-posta türünü belirler.
     * English: Determines the email type based on patterns.
     */
    private fun determineEmailTypeFromPatterns(email: RawEmail): EmailType {
        val bodyText = email.bodySnippet ?: email.snippet ?: ""
        val content = "${email.subject} $bodyText".lowercase()

        // İptal kalıplarıyla kontrol et
        for (pattern in cancelPatterns) {
            if (pattern.containsMatchIn(content)) {
                return EmailType.SUBSCRIPTION_CANCEL
            }
        }

        // Başlangıç kalıplarıyla kontrol et
        for (pattern in startPatterns) {
            if (pattern.containsMatchIn(content)) {
                return EmailType.SUBSCRIPTION_START
            }
        }

        // Ödeme kalıplarıyla kontrol et
        for (pattern in paymentPatterns) {
            if (pattern.containsMatchIn(content)) {
                return EmailType.PAYMENT_CONFIRMATION
            }
        }

        // Reklam kalıplarıyla kontrol et
        for (pattern in promotionalPatterns) {
            if (pattern.containsMatchIn(content)) {
                return EmailType.PROMOTIONAL_MESSAGE
            }
        }

        return EmailType.UNKNOWN
    }

    /**
     * Turkish: Hybrid Approach email türünü EmailType enum'una dönüştürür.
     * English: Converts Hybrid Approach email type to EmailType enum.
     */
    private fun convertHybridEmailTypeToEmailType(hybridEmailType: String): EmailType {
        return when (hybridEmailType) {
            "subscription_start" -> EmailType.SUBSCRIPTION_START
            "subscription_cancel" -> EmailType.SUBSCRIPTION_CANCEL
            "subscription_renewal" -> EmailType.SUBSCRIPTION_RENEWAL
            "payment_confirmation" -> EmailType.PAYMENT_CONFIRMATION
            "welcome_message" -> EmailType.WELCOME_MESSAGE
            "promotional_offer" -> EmailType.PROMOTIONAL_MESSAGE
            "billing_notification" -> EmailType.PAYMENT_CONFIRMATION
            "account_notification" -> EmailType.GENERAL_NOTIFICATION
            "service_update" -> EmailType.GENERAL_NOTIFICATION
            "other" -> EmailType.UNKNOWN
            else -> {
                Log.w("SubscriptionClassifier", "Unknown hybrid email type: $hybridEmailType, defaulting to UNKNOWN")
                EmailType.UNKNOWN
            }
        }
    }

    /**
     * Turkish: Abonelik türünü belirler.
     * English: Determines the subscription type.
     */
    private fun determineSubscriptionType(
        subscriptionResult: ClassificationResult,
        paidSubscriptionResult: ClassificationResult
    ): SubscriptionType {
        // Önce ücretli abonelik kontrolü
        if (paidSubscriptionResult.label == "paid_subscription" && paidSubscriptionResult.score > 0.65f) {
            return SubscriptionType.PAID
        }

        // Sonra genel abonelik türü kontrolü
        return when (subscriptionResult.label) {
            "paid_subscription" -> SubscriptionType.PAID
            "free_subscription" -> SubscriptionType.FREE
            "promotional" -> SubscriptionType.PROMOTIONAL
            "not_subscription" -> SubscriptionType.NOT_SUBSCRIPTION
            else -> SubscriptionType.UNKNOWN
        }
    }

    /**
     * E-posta içeriğini sınıflandırma için hazırlar
     */
    private fun prepareEmailContentForClassification(email: RawEmail): String {
        val bodyContent = when {
            email.bodySnippet != null -> email.bodySnippet
            email.snippet != null -> email.snippet
            else -> email.bodyPlainText.take(500)
        }
        return "Subject: ${email.subject}\nFrom: ${email.from}\nContent: $bodyContent"
    }

    /**
     * E-posta için servis adını belirler
     */
    private fun determineServiceName(email: RawEmail, patterns: List<SubscriptionPatternEntity>): String {
        // Önce kalıplarla eşleşmeyi dene
        for (pattern in patterns) {
            if (matchesPattern(email, pattern)) {
                return pattern.serviceName
            }
        }

        // Kalıplarla eşleşmediyse, domain adından tahmin et
        return extractGeneralServiceName(email.from, email.subject, email.bodySnippet)
    }

    /**
     * E-posta adresinden domain adını çıkarır
     */
    private fun extractDomain(emailAddress: String): String? {
        val domainMatch = Regex("@([a-zA-Z0-9.-]+)").find(emailAddress)
        return domainMatch?.groupValues?.get(1)
    }

    /**
     * Şirket adlarını normalize eder - duplikasyonları önler
     */
    private fun normalizeCompanyName(companyName: String): String {
        val normalized = companyName.lowercase().trim()

        return when {
            // Spotify varyasyonları
            normalized.contains("spotify") -> "spotify"

            // Netflix varyasyonları
            normalized.contains("netflix") -> "netflix"

            // Disney varyasyonları
            normalized.contains("disney") -> "disney"

            // Adobe varyasyonları
            normalized.contains("adobe") -> "adobe"

            // Microsoft varyasyonları
            normalized.contains("microsoft") -> "microsoft"

            // Apple varyasyonları
            normalized.contains("apple") -> "apple"

            // Amazon varyasyonları
            normalized.contains("amazon") -> "amazon"

            // Google varyasyonları
            normalized.contains("google") -> "google"

            // Scribd varyasyonları
            normalized.contains("scribd") -> "scribd"

            // Financial Times varyasyonları (sadece gerçek Financial Times)
            normalized.contains("financial") && normalized.contains("times") -> "ft"

            // YouTube varyasyonları
            normalized.contains("youtube") -> "youtube"

            // LinkedIn varyasyonları
            normalized.contains("linkedin") -> "linkedin"

            // Diğer durumlarda orijinal adı döndür
            else -> companyName.lowercase()
        }
    }

    /**
     * TÜM DOMAIN'LERİ KABUL ET - AI sınıflandırması yapacak
     */
    private fun isTrustedSubscriptionDomain(domain: String): Boolean {
        // TÜM DOMAIN'LERİ KABUL ET - Domain filtresi yok
        return true
    }

    /**
     * Ücretli abonelik şirketleri listesi - sadece bu şirketler abonelik olarak değerlendirilir
     */
    private val paidSubscriptionCompanies = setOf(
        // Video Streaming
        "netflix", "amazon", "disney", "disneyplus", "hbo", "hbomax", "max", "hulu", "peacock",
        "paramount", "crunchyroll", "starz", "espn", "dazn", "mubi", "jiocinema", "globoplay",
        "viu", "discovery", "zee5", "rtl", "ivi", "shahid", "youtube", "exxen", "blutv",
        "gain", "dsmart", "digiturk", "tivibu", "tod",

        // Music Streaming
        "spotify", "apple", "tidal", "deezer", "soundcloud",

        // Books & Audio
        "audible", "kindle", "storytel", "medium", "scribd",

        // Education
        "coursera", "udemy", "linkedin", "masterclass", "codecademy", "udacity", "skillshare",
        "duolingo", "babbel", "rosetta", "brilliant", "photomath", "pluralsight",

        // Health & Fitness
        "peloton", "classpass", "fitbit", "calm", "headspace", "noom", "tonal", "mirror",
        "zwift", "betterhelp", "talkspace", "nike", "strava",

        // News & Finance
        "bloomberg", "wsj", "ft", "economist", "nytimes", "washingtonpost", "forbes",
        "barrons", "yahoo", "seeking", "morningstar", "reuters",

        // Gaming
        "xbox", "playstation", "nintendo", "ea", "ubisoft", "twitch", "discord", "roblox",

        // Software & SaaS
        "microsoft", "google", "adobe", "salesforce", "aws", "azure", "zoom", "slack",
        "atlassian", "dropbox", "box", "github", "gitlab", "canva", "asana", "trello",
        "docusign", "eventbrite", "grammarly", "webflow", "hubspot", "mailchimp",
        "surveymonkey", "zendesk", "servicenow", "twilio", "autodesk", "shopify",

        // Retail & Subscription Boxes
        "prime", "costco", "sams", "hellofresh", "blueapron", "dollarshave", "birchbox",
        "barkbox", "graze",

        // Content Platforms
        "patreon", "onlyfans"
    )

    /**
     * AI destekli şirket sınıflandırması - sadece ücretli abonelik şirketlerini tespit eder
     * GÜÇLENDIRILMIŞ VERSİYON: Daha detaylı analiz
     */
    private fun createLocalCompanyResult(domain: String): com.example.abonekaptanmobile.data.remote.model.ClassificationResult {
        val domainLower = domain.lowercase()

        // FTMO gibi yanlış eşleşmeleri önle
        if (domainLower.contains("ftmo") || domainLower.contains("forex") ||
            domainLower.contains("trading") || domainLower.contains("investment") ||
            domainLower.contains("crypto") || domainLower.contains("bitcoin") ||
            domainLower.contains("bybit") || domainLower.contains("binance")) {
            return com.example.abonekaptanmobile.data.remote.model.ClassificationResult(
                label = "unknown",
                score = 0.05f // Çok düşük confidence - bunlar abonelik servisi değil
            )
        }

        // Önce domain'de bilinen şirket var mı kontrol et
        val detectedCompany = paidSubscriptionCompanies.find { company ->
            when (company) {
                "ft" -> {
                    // Financial Times için özel kontrol - sadece gerçek FT domainleri
                    domainLower.contains("ft.com") ||
                    domainLower.contains("financialtimes.com") ||
                    (domainLower.contains("financial") && domainLower.contains("times"))
                }
                else -> {
                    domainLower.contains(company) ||
                    domainLower.contains("$company.") ||
                    domainLower.startsWith(company) ||
                    domainLower.endsWith(company)
                }
            }
        }

        return if (detectedCompany != null) {
            // Bilinen ücretli abonelik şirketi
            val confidence = when (detectedCompany) {
                in listOf("netflix", "spotify", "disney", "adobe", "microsoft", "scribd") -> 0.98f
                in listOf("youtube", "apple", "amazon") -> 0.95f
                "ft" -> if (domainLower.contains("ft.com") || domainLower.contains("financialtimes")) 0.98f else 0.70f
                else -> 0.90f
            }

            com.example.abonekaptanmobile.data.remote.model.ClassificationResult(
                label = detectedCompany,
                score = confidence
            )
        } else {
            // Bilinmeyen şirket - abonelik değil
            com.example.abonekaptanmobile.data.remote.model.ClassificationResult(
                label = "unknown",
                score = 0.1f // Çok düşük confidence
            )
        }
    }

    /**
     * HuggingFace API hatası durumunda fallback email türü sonucu oluşturur
     */
    private fun createFallbackEmailTypeResult(emailContent: String): com.example.abonekaptanmobile.data.remote.model.ClassificationResult {
        val content = emailContent.lowercase()

        val emailType = when {
            content.contains("subscription") && (content.contains("start") || content.contains("welcome") || content.contains("activated")) -> "subscription_start"
            content.contains("cancel") || content.contains("unsubscribe") || content.contains("terminated") -> "subscription_cancel"
            content.contains("payment") && (content.contains("confirm") || content.contains("receipt") || content.contains("invoice")) -> "payment_confirmation"
            content.contains("trial") && content.contains("end") -> "trial_ending"
            content.contains("renewal") || content.contains("renew") -> "subscription_renewal"
            content.contains("upgrade") || content.contains("plan") -> "plan_change"
            else -> "subscription_related"
        }

        return com.example.abonekaptanmobile.data.remote.model.ClassificationResult(
            label = emailType,
            score = 0.85f // Yüksek confidence çünkü kelime eşleştirmesi güvenilir
        )
    }

    /**
     * Yerel detaylı email türü sınıflandırması - gelişmiş pattern matching
     * GÜÇLENDIRILMIŞ VERSİYON: Daha kapsamlı ve detaylı analiz
     */
    private fun createLocalDetailedEmailTypeResult(emailContent: String, companyName: String): com.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult {
        val content = emailContent.lowercase()
        val subject = emailContent.take(200).lowercase() // İlk 200 karakter subject olarak kabul et

        // Çoklu pattern matching ile daha doğru tespit - GENİŞLETİLMİŞ PATTERN'LAR
        val patterns = mapOf(
            "subscription_start" to listOf(
                "welcome", "başladı", "activated", "subscription.*start", "üyelik.*başla",
                "hoş geldin", "welcome to", "subscription.*active", "plan.*active",
                "account.*created", "hesap.*oluşturuldu", "getting.*started", "başlangıç",
                "your.*subscription.*is.*now.*active", "aboneliğiniz.*aktif"
            ),
            "subscription_cancel" to listOf(
                "cancel", "iptal", "unsubscribe", "terminated", "ended", "sona erdi",
                "subscription.*cancel", "üyelik.*iptal", "abonelik.*iptal", "cancelled",
                "subscription.*ended", "abonelik.*sona.*erdi", "we're.*sorry.*to.*see.*you.*go",
                "your.*subscription.*has.*been.*cancelled", "aboneliğiniz.*iptal.*edildi",
                "subscription.*will.*end", "abonelik.*sona.*erecek", "final.*bill",
                "last.*payment", "son.*ödeme", "farewell", "goodbye", "elveda"
            ),
            "payment_confirmation" to listOf(
                "payment.*confirm", "ödeme.*onay", "receipt", "invoice", "fatura",
                "makbuz", "payment.*successful", "ödeme.*başarılı", "charged",
                "billing.*confirmation", "fatura.*onayı", "payment.*received",
                "ödeme.*alındı", "transaction.*complete", "işlem.*tamamlandı"
            ),
            "trial_ending" to listOf(
                "trial.*end", "deneme.*bitiyor", "trial.*expir", "free.*trial.*end",
                "ücretsiz.*deneme.*bitiyor", "trial.*sona", "trial.*period.*ending",
                "deneme.*süresi.*bitiyor", "your.*free.*trial.*expires", "trial.*will.*end"
            ),
            "subscription_renewal" to listOf(
                "renewal", "yenileme", "renew", "subscription.*renew", "abonelik.*yenile",
                "auto.*renew", "otomatik.*yenileme", "subscription.*renewed",
                "abonelik.*yenilendi", "billing.*cycle", "fatura.*döngüsü",
                "next.*payment", "sonraki.*ödeme", "recurring.*payment"
            ),
            "plan_change" to listOf(
                "upgrade", "downgrade", "plan.*change", "plan.*değiş", "yükselt",
                "plan.*upgrade", "subscription.*change", "plan.*updated",
                "plan.*güncellendi", "subscription.*modified", "abonelik.*değiştirildi"
            ),
            "promotional" to listOf(
                "offer", "discount", "sale", "promo", "kampanya", "indirim",
                "special.*offer", "özel.*teklif", "limited.*time", "sınırlı.*süre",
                "deal", "anlaşma", "save", "tasarruf", "free.*trial", "ücretsiz.*deneme",
                "don't.*miss.*out", "kaçırma", "exclusive", "özel", "bonus"
            )
        )

        // Her pattern için score hesapla
        val scores = mutableMapOf<String, Float>()

        for ((emailType, patternList) in patterns) {
            var score = 0f
            var matchCount = 0

            for (pattern in patternList) {
                val regex = Regex(pattern, RegexOption.IGNORE_CASE)
                if (regex.containsMatchIn(content)) {
                    matchCount++
                    // Subject'te bulunursa daha yüksek puan
                    score += if (regex.containsMatchIn(subject)) 0.3f else 0.2f
                }
            }

            // Şirket spesifik bonus
            if (companyName != "unknown" && companyName != "other") {
                score += 0.1f
            }

            // Pattern yoğunluğuna göre bonus
            if (matchCount > 1) {
                score += matchCount * 0.1f
            }

            scores[emailType] = minOf(score, 1.0f)
        }

        // En yüksek skorlu türü seç
        val primaryType = scores.maxByOrNull { it.value }
        val primaryLabel = primaryType?.key ?: "subscription_related"
        val primaryScore = primaryType?.value ?: 0.5f

        // Tüm sonuçları oluştur
        val allResults = scores.map { (type, score) ->
            com.example.abonekaptanmobile.data.remote.model.ClassificationResult(
                label = type,
                score = score
            )
        }.sortedByDescending { it.score }

        return com.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult(
            primaryLabel = primaryLabel,
            primaryScore = primaryScore,
            allResults = allResults
        )
    }

    /**
     * HuggingFace API hatası durumunda fallback detaylı email türü sonucu oluşturur
     */
    private fun createFallbackDetailedEmailTypeResult(emailContent: String): com.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult {
        val primaryResult = createFallbackEmailTypeResult(emailContent)

        return com.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult(
            primaryLabel = primaryResult.label,
            primaryScore = primaryResult.score,
            allResults = listOf(primaryResult)
        )
    }

    /**
     * 🤖 Gelişmiş AI analizi - Gerçek AI ile detaylı e-posta analizi
     */
    private suspend fun performAdvancedAIAnalysis(email: RawEmail): AIClassificationResult {
        return kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.IO) {
            try {
                // Gelişmiş AI analizi simülasyonu (gerçek implementasyon için OpenAI API gerekli)
                val result = simulateAdvancedAIResponse(email)

                Log.d("SubscriptionClassifier", "🤖 AI Detaylı Analiz Tamamlandı: ${result.company} - ${result.emailType} (${String.format("%.2f", result.confidence)})")

                result

            } catch (e: Exception) {
                Log.e("SubscriptionClassifier", "🤖 AI analysis failed", e)
                AIClassificationResult(
                    company = "unknown",
                    emailType = "subscription_start",
                    confidence = 0.0,
                    reasoning = "AI analysis failed: ${e.message}"
                )
            }
        }
    }

    /**
     * 🤖 Gelişmiş AI analizi simülasyonu - Gerçek AI mantığı
     */
    private fun simulateAdvancedAIResponse(email: RawEmail): AIClassificationResult {
        val bodyText = email.bodySnippet ?: email.snippet ?: ""
        val content = "${email.subject} $bodyText".lowercase()
        val domain = extractDomain(email.from)?.lowercase() ?: ""

        // 🤖 AI MANTIK: Çok daha akıllı analiz

        // 1. Bilinen abonelik şirketlerini tespit et
        val knownSubscriptionServices = mapOf(
            "netflix" to listOf("netflix.com", "netflix"),
            "spotify" to listOf("spotify.com", "spotify"),
            "disney" to listOf("disney.com", "disneyplus", "disney+"),
            "scribd" to listOf("scribd.com", "scribd"),
            "adobe" to listOf("adobe.com", "adobe"),
            "microsoft" to listOf("microsoft.com", "office365", "outlook"),
            "apple" to listOf("apple.com", "icloud", "itunes"),
            "amazon" to listOf("amazon.com", "prime", "aws"),
            "google" to listOf("google.com", "youtube", "gmail"),
            "linkedin" to listOf("linkedin.com", "linkedin")
        )

        var detectedCompany = "unknown"
        var confidence = 0.0

        // Domain'den şirket tespit et
        for ((company, domains) in knownSubscriptionServices) {
            if (domains.any { domain.contains(it) || content.contains(it) }) {
                detectedCompany = company
                confidence = when (company) {
                    "netflix", "spotify", "disney", "scribd", "adobe" -> 0.95
                    "microsoft", "apple", "amazon" -> 0.90
                    "google", "linkedin" -> 0.85
                    else -> 0.80
                }
                break
            }
        }

        // 2. E-posta türünü akıllıca tespit et
        val emailType = when {
            // İptal pattern'ları - çok detaylı
            content.contains("cancel") || content.contains("iptal") ||
            content.contains("unsubscribe") || content.contains("terminated") ||
            content.contains("ended") || content.contains("sona erdi") ||
            content.contains("subscription.*cancel") || content.contains("üyelik.*iptal") ||
            content.contains("abonelik.*iptal") || content.contains("cancelled") ||
            content.contains("subscription.*ended") || content.contains("abonelik.*sona.*erdi") ||
            content.contains("we're.*sorry.*to.*see.*you.*go") ||
            content.contains("your.*subscription.*has.*been.*cancelled") ||
            content.contains("aboneliğiniz.*iptal.*edildi") ||
            content.contains("subscription.*will.*end") || content.contains("abonelik.*sona.*erecek") ||
            content.contains("final.*bill") || content.contains("last.*payment") ||
            content.contains("son.*ödeme") || content.contains("farewell") ||
            content.contains("goodbye") || content.contains("elveda") -> {
                confidence *= 0.95 // İptal tespiti çok önemli
                "subscription_cancel"
            }

            // Başlangıç pattern'ları
            content.contains("welcome") || content.contains("başladı") ||
            content.contains("activated") || content.contains("subscription.*start") ||
            content.contains("üyelik.*başla") || content.contains("hoş geldin") ||
            content.contains("welcome to") || content.contains("subscription.*active") ||
            content.contains("plan.*active") || content.contains("account.*created") ||
            content.contains("hesap.*oluşturuldu") || content.contains("getting.*started") ||
            content.contains("başlangıç") || content.contains("your.*subscription.*is.*now.*active") ||
            content.contains("aboneliğiniz.*aktif") -> "subscription_start"

            // Yenileme pattern'ları
            content.contains("renewal") || content.contains("yenileme") ||
            content.contains("renew") || content.contains("subscription.*renew") ||
            content.contains("abonelik.*yenile") || content.contains("auto.*renew") ||
            content.contains("otomatik.*yenileme") || content.contains("subscription.*renewed") ||
            content.contains("abonelik.*yenilendi") || content.contains("billing.*cycle") ||
            content.contains("fatura.*döngüsü") || content.contains("next.*payment") ||
            content.contains("sonraki.*ödeme") || content.contains("recurring.*payment") -> "subscription_renewal"

            // Ödeme onayı
            content.contains("payment.*confirm") || content.contains("ödeme.*onay") ||
            content.contains("receipt") || content.contains("invoice") ||
            content.contains("fatura") || content.contains("makbuz") ||
            content.contains("payment.*successful") || content.contains("ödeme.*başarılı") ||
            content.contains("charged") || content.contains("billing.*confirmation") ||
            content.contains("fatura.*onayı") || content.contains("payment.*received") ||
            content.contains("ödeme.*alındı") || content.contains("transaction.*complete") ||
            content.contains("işlem.*tamamlandı") -> "payment_confirmation"

            // Promosyon
            content.contains("offer") || content.contains("discount") ||
            content.contains("sale") || content.contains("promo") ||
            content.contains("kampanya") || content.contains("indirim") ||
            content.contains("special.*offer") || content.contains("özel.*teklif") ||
            content.contains("limited.*time") || content.contains("sınırlı.*süre") ||
            content.contains("deal") || content.contains("anlaşma") ||
            content.contains("save") || content.contains("tasarruf") ||
            content.contains("free.*trial") || content.contains("ücretsiz.*deneme") ||
            content.contains("don't.*miss.*out") || content.contains("kaçırma") ||
            content.contains("exclusive") || content.contains("özel") ||
            content.contains("bonus") -> "promotional"

            else -> "subscription_start"
        }

        // 3. Confidence ayarlaması
        if (detectedCompany == "unknown") {
            confidence = 0.05 // Bilinmeyen şirketler çok düşük confidence
        }

        // FTMO ve forex şirketlerini filtrele
        if (domain.contains("ftmo") || domain.contains("forex") ||
            domain.contains("trading") || domain.contains("bybit") ||
            domain.contains("binance") || content.contains("forex") ||
            content.contains("trading") || content.contains("crypto")) {
            detectedCompany = "unknown"
            confidence = 0.02
        }

        // LinkedIn ve Google Services filtreleme
        if (detectedCompany == "linkedin" || detectedCompany == "google") {
            confidence = 0.45 // Threshold'un altında
        }

        return AIClassificationResult(
            company = detectedCompany,
            emailType = emailType,
            confidence = confidence,
            reasoning = "🤖 AI Analysis: Domain=$domain, Company=$detectedCompany, Type=$emailType"
        )
    }

    /**
     * AI sonucunu hybrid validation format'ına dönüştür
     */
    private fun convertAIResultToHybridValidation(aiResult: AIClassificationResult): com.example.abonekaptanmobile.data.remote.model.HybridValidationResult {
        // Dummy email type result oluştur
        val emailTypeResult = com.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult(
            primaryLabel = aiResult.emailType,
            primaryScore = aiResult.confidence.toFloat(),
            allResults = emptyList()
        )

        return com.example.abonekaptanmobile.data.remote.model.HybridValidationResult(
            companyName = aiResult.company,
            companyConfidence = aiResult.confidence.toFloat(),
            emailType = aiResult.emailType,
            emailTypeConfidence = aiResult.confidence.toFloat(),
            overallConfidence = aiResult.confidence.toFloat(),
            isReliable = aiResult.confidence >= 0.6,
            detailedEmailTypeResult = emailTypeResult
        )
    }

    /**
     * AI Classification Result data class
     */
    data class AIClassificationResult(
        val company: String,
        val emailType: String,
        val confidence: Double,
        val reasoning: String
    )

    /**
     * Yerel doğrulama sistemi - gelişmiş güvenilirlik kontrolü
     */
    private fun createLocalValidation(
        companyResult: com.example.abonekaptanmobile.data.remote.model.ClassificationResult,
        emailTypeResult: com.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult
    ): com.example.abonekaptanmobile.data.remote.model.HybridValidationResult {
        // Ağırlıklı confidence hesaplama
        val companyWeight = 0.6f // Şirket tespiti daha önemli
        val emailTypeWeight = 0.4f // Email türü tespiti

        val overallConfidence = (companyResult.score * companyWeight) + (emailTypeResult.primaryScore * emailTypeWeight)

        // Güvenilirlik kriterleri
        val isReliable = when {
            // Çok yüksek confidence
            overallConfidence > 0.9f -> true
            // Orta-yüksek confidence + bilinen şirket
            overallConfidence > 0.7f && companyResult.label != "unknown" -> true
            // Düşük confidence
            overallConfidence < 0.5f -> false
            // Bilinmeyen şirket + düşük email type confidence
            companyResult.label == "unknown" && emailTypeResult.primaryScore < 0.6f -> false
            // Diğer durumlar
            else -> overallConfidence > 0.75f
        }

        return com.example.abonekaptanmobile.data.remote.model.HybridValidationResult(
            companyName = companyResult.label,
            companyConfidence = companyResult.score,
            emailType = emailTypeResult.primaryLabel,
            emailTypeConfidence = emailTypeResult.primaryScore,
            overallConfidence = overallConfidence,
            isReliable = isReliable,
            detailedEmailTypeResult = emailTypeResult
        )
    }

    /**
     * HuggingFace API hatası durumunda fallback doğrulama sonucu oluşturur
     */
    private fun createFallbackValidation(
        companyResult: com.example.abonekaptanmobile.data.remote.model.ClassificationResult,
        emailTypeResult: com.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult
    ): com.example.abonekaptanmobile.data.remote.model.HybridValidationResult {
        val overallConfidence = (companyResult.score + emailTypeResult.primaryScore) / 2
        val isReliable = overallConfidence > 0.8f

        return com.example.abonekaptanmobile.data.remote.model.HybridValidationResult(
            companyName = companyResult.label,
            companyConfidence = companyResult.score,
            emailType = emailTypeResult.primaryLabel,
            emailTypeConfidence = emailTypeResult.primaryScore,
            overallConfidence = overallConfidence,
            isReliable = isReliable,
            detailedEmailTypeResult = emailTypeResult
        )
    }

    private fun matchesPattern(email: RawEmail, pattern: SubscriptionPatternEntity): Boolean {
        val bodyText = when {
            email.bodySnippet != null -> email.bodySnippet
            email.snippet != null -> email.snippet
            else -> ""
        }

        val contentToSearch = when (pattern.patternType) {
            PatternType.DOMAIN -> email.from.lowercase()
            PatternType.SENDER_EMAIL -> email.from.lowercase()
            PatternType.SUBJECT_KEYWORD -> email.subject.lowercase()

            PatternType.BODY_KEYWORD -> bodyText!!.lowercase()
            PatternType.COMBINED, PatternType.UNKNOWN -> "${email.from} ${email.subject} $bodyText".lowercase()
            else -> {
                Log.w("SubscriptionClassifier", "Unknown pattern type: ${pattern.patternType} for pattern ID: ${pattern.id}. Defaulting to full content search.")
                "${email.from} ${email.subject} $bodyText".lowercase()
            }
        }
        return try {
            // Regex'in başında ve sonunda .* ekleyerek kısmi eşleşmelere izin verelim (eğer pattern zaten bunu içermiyorsa)
            // Ancak bu, regex'lerin nasıl yazıldığına bağlı. Eğer regex'ler zaten tam eşleşme içinse bu gereksiz.
            // Şimdilik orijinal haliyle bırakıyorum.
            val regex = Regex(pattern.regexPattern, setOf(RegexOption.IGNORE_CASE, RegexOption.DOT_MATCHES_ALL))
            regex.containsMatchIn(contentToSearch)
        } catch (e: PatternSyntaxException) {
            Log.e("SubscriptionClassifier", "Invalid regex: '${pattern.regexPattern}' for service '${pattern.serviceName}' (ID: ${pattern.id}). Error: ${e.message}")
            false
        }
    }

    // Bu fonksiyon, verilen kalıpların hepsi abonelik kalıbıysa (isSubscription=true) çağrılmalı.
    private fun applyAndCollectSubscriptionPatterns(
        emails: List<RawEmail>,
        subscriptionPatterns: List<SubscriptionPatternEntity>,
        collector: MutableMap<String, MutableList<ClassifiedEmail>>
    ): MutableList<RawEmail> {
        val remaining = mutableListOf<RawEmail>()
        emails.forEach { email ->
            var matched = false
            for (pattern in subscriptionPatterns) {
                // Bu fonksiyona gelen tüm pattern'ların isSubscription = true olduğunu varsayıyoruz.
                if (matchesPattern(email, pattern)) {
                    val detail = ClassifiedEmail(
                        rawEmail = email,
                        identifiedService = pattern.serviceName,
                        isLikelySubscription = true, // Çünkü bunlar abonelik kalıpları
                        matchedPatternId = pattern.id
                    )
                    collector.getOrPut(pattern.serviceName) { mutableListOf() }.add(detail)
                    Log.d("SubscriptionClassifier", "Email ID ${email.id} (Sub: ${email.subject.take(20)}) matched SUB pattern: '${pattern.serviceName}'. Added to collector.")
                    matched = true
                    break // Bu e-posta için kalıp bulundu
                }
            }
            if (!matched) {
                remaining.add(email)
            }
        }
        return remaining
    }

    /**
     * Turkish: Sınıflandırılmış e-postalardan abonelik öğelerini oluşturur.
     * English: Creates subscription items from classified emails.
     */
    private suspend fun createSubscriptionItemsFromDetails(
        classifiedEmailDetails: Map<String, List<ClassifiedEmail>>
    ): List<SubscriptionItem> = coroutineScope {
        val resultList = mutableListOf<SubscriptionItem>()
        Log.d("SubscriptionClassifier", "Creating items from ${classifiedEmailDetails.size} identified services.")

        classifiedEmailDetails.forEach { (serviceName, detailsForService) ->
            // Bu servise ait tüm ham e-postaları al.
            val subscriptionRelatedRawEmails = detailsForService.map { it.rawEmail }.distinctBy { it.id }

            if (subscriptionRelatedRawEmails.isEmpty()) {
                Log.w("SubscriptionClassifier", "No raw emails for service: $serviceName after map. This shouldn't happen if detailsForService is not empty.")
                return@forEach
            }

            // E-postaları tarih sırasına göre sırala (en yeniden en eskiye)
            val sortedRawEmails = subscriptionRelatedRawEmails.sortedByDescending { it.date }
            val lastEmailDate = sortedRawEmails.first().date
            val emailCount = sortedRawEmails.size
            val relatedEmailIds = sortedRawEmails.map { it.id }

            // Sınıflandırılmış e-postaları tarih sırasına göre sırala (en yeniden en eskiye)
            val sortedClassifiedEmails = detailsForService.sortedByDescending { it.rawEmail.date }

            var subscriptionStartDate: Long? = null
            var latestCancellationDate: Long? = null
            var latestRenewalDate: Long? = null
            var latestPaymentDate: Long? = null
            var latestActivityDate = 0L
            var isPaidSubscription = false

            // Her e-posta için abonelik durumunu kontrol et
            for (classifiedEmail in sortedClassifiedEmails) {
                val email = classifiedEmail.rawEmail
                val emailType = classifiedEmail.emailType

                // E-posta türüne göre işlem yap
                when (emailType) {
                    EmailType.SUBSCRIPTION_START -> {
                        // En eski başlangıç tarihini bul
                        if (subscriptionStartDate == null || email.date < subscriptionStartDate) {
                            subscriptionStartDate = email.date
                            Log.d("SubscriptionClassifier", "Found subscription start date for $serviceName: ${email.date}")
                        }

                        // Aktivite tarihini güncelle
                        if (email.date > latestActivityDate) {
                            latestActivityDate = email.date
                        }
                    }
                    EmailType.SUBSCRIPTION_CANCEL -> {
                        // En yeni iptal tarihini bul
                        if (latestCancellationDate == null || email.date > latestCancellationDate) {
                            latestCancellationDate = email.date
                            Log.d("SubscriptionClassifier", "Found subscription cancellation date for $serviceName: ${email.date}")
                        }
                    }
                    EmailType.SUBSCRIPTION_RENEWAL -> {
                        // En yeni yenileme tarihini bul
                        if (latestRenewalDate == null || email.date > latestRenewalDate) {
                            latestRenewalDate = email.date
                            Log.d("SubscriptionClassifier", "Found subscription renewal date for $serviceName: ${email.date}")
                        }

                        // Aktivite tarihini güncelle
                        if (email.date > latestActivityDate) {
                            latestActivityDate = email.date
                        }
                    }
                    EmailType.PAYMENT_CONFIRMATION -> {
                        // En yeni ödeme tarihini bul
                        if (latestPaymentDate == null || email.date > latestPaymentDate) {
                            latestPaymentDate = email.date
                            Log.d("SubscriptionClassifier", "Found payment confirmation date for $serviceName: ${email.date}")
                        }

                        // Aktivite tarihini güncelle
                        if (email.date > latestActivityDate) {
                            latestActivityDate = email.date
                        }

                        // Ücretli abonelik olarak işaretle
                        isPaidSubscription = true
                    }
                    EmailType.WELCOME_MESSAGE -> {
                        // Başlangıç tarihi yoksa, hoşgeldiniz mesajını başlangıç tarihi olarak kullan
                        if (subscriptionStartDate == null) {
                            subscriptionStartDate = email.date
                            Log.d("SubscriptionClassifier", "Using welcome message as subscription start date for $serviceName: ${email.date}")
                        }

                        // Aktivite tarihini güncelle
                        if (email.date > latestActivityDate) {
                            latestActivityDate = email.date
                        }
                    }
                    else -> {
                        // Diğer e-posta türleri için aktivite tarihini güncelle
                        if (email.date > latestActivityDate) {
                            latestActivityDate = email.date
                        }
                    }
                }

                // Ücretli abonelik kontrolü
                if (classifiedEmail.isPaidSubscription) {
                    isPaidSubscription = true
                }
            }

            // Fallback: Eğer hiçbir e-posta türü belirlenemezse, eski yöntemi kullan
            if (latestActivityDate == 0L) {
                for (email in sortedRawEmails) {
                    try {
                        val emailContent = prepareEmailContentForClassification(email)
                        val statusResult = huggingFaceRepository.classifySubscriptionStatus(emailContent)

                        when (statusResult.label) {
                            "subscription_start" -> {
                                if (subscriptionStartDate == null || email.date < subscriptionStartDate) {
                                    subscriptionStartDate = email.date
                                }
                            }
                            "subscription_cancel" -> {
                                if (latestCancellationDate == null || email.date > latestCancellationDate) {
                                    latestCancellationDate = email.date
                                }
                            }
                            "subscription_renewal" -> {
                                if (latestRenewalDate == null || email.date > latestRenewalDate) {
                                    latestRenewalDate = email.date
                                }
                                if (email.date > latestActivityDate) {
                                    latestActivityDate = email.date
                                }
                            }
                            "payment_confirmation" -> {
                                if (latestPaymentDate == null || email.date > latestPaymentDate) {
                                    latestPaymentDate = email.date
                                }
                                if (email.date > latestActivityDate) {
                                    latestActivityDate = email.date
                                }
                                isPaidSubscription = true
                            }
                        }
                    } catch (e: Exception) {
                        Log.e("SubscriptionClassifier", "Error in fallback classification: ${e.message}", e)
                    }
                }
            }

            // En son aktivite tarihini belirle
            if (latestRenewalDate != null && (latestActivityDate == 0L || latestRenewalDate > latestActivityDate)) {
                latestActivityDate = latestRenewalDate
            }

            if (latestPaymentDate != null && (latestActivityDate == 0L || latestPaymentDate > latestActivityDate)) {
                latestActivityDate = latestPaymentDate
            }

            if (latestActivityDate == 0L && sortedRawEmails.isNotEmpty()) {
                latestActivityDate = lastEmailDate
            }

            // Abonelik durumunu belirle
            val status: SubscriptionStatus
            if (latestCancellationDate != null) {
                // İptal tarihi varsa, son aktivite tarihine göre kontrol et
                if (latestActivityDate > latestCancellationDate ||
                    (latestRenewalDate != null && latestRenewalDate > latestCancellationDate) ||
                    (latestPaymentDate != null && latestPaymentDate > latestCancellationDate)) {
                    // İptal sonrası aktivite varsa, aktif olarak işaretle
                    status = SubscriptionStatus.ACTIVE
                } else {
                    // İptal sonrası aktivite yoksa, iptal edilmiş olarak işaretle
                    status = SubscriptionStatus.CANCELLED
                }
            } else {
                // İptal tarihi yoksa, son aktivite tarihine göre kontrol et
                val timeSinceLastActivity = System.currentTimeMillis() - latestActivityDate
                status = if (latestActivityDate > 0 && timeSinceLastActivity > inactivityThresholdMillis) {
                    // Son aktivite üzerinden belirli bir süre geçmişse, unutulmuş olarak işaretle
                    SubscriptionStatus.FORGOTTEN
                } else {
                    // Son aktivite yakın zamandaysa, aktif olarak işaretle
                    SubscriptionStatus.ACTIVE
                }
            }

            Log.i("SubscriptionClassifier", "FINAL ITEM for '$serviceName': Status=$status, Count=$emailCount, " +
                    "LastEmailDate=$lastEmailDate, StartDate=$subscriptionStartDate, CancelDate=$latestCancellationDate, " +
                    "LastActivity=$latestActivityDate, IsPaid=$isPaidSubscription")

            resultList.add(
                SubscriptionItem(
                    serviceName = serviceName,
                    emailCount = emailCount,
                    lastEmailDate = lastEmailDate,
                    status = status,
                    cancellationDate = if (status == SubscriptionStatus.CANCELLED) latestCancellationDate else null,
                    relatedEmailIds = relatedEmailIds,
                    subscriptionStartDate = subscriptionStartDate
                )
            )
        }

        // Önce iptal edilmemiş abonelikleri, sonra en son e-posta tarihine göre sırala
        return@coroutineScope resultList.sortedWith(
            compareByDescending<SubscriptionItem> { it.status != SubscriptionStatus.CANCELLED }
                .thenByDescending { it.lastEmailDate }
        )
    }

    private fun extractGeneralServiceName(from: String, subject: String, bodySnippet: String?): String {
        val domainMatch = Regex("@([a-zA-Z0-9.-]+)").find(from)
        var serviceNameFromDomainPart = domainMatch?.groupValues?.get(1)?.substringBeforeLast(".")?.substringAfterLast(".")
        if (serviceNameFromDomainPart != null && serviceNameFromDomainPart.length < 3 && domainMatch?.groupValues?.get(1)?.contains(".") == true) {
            serviceNameFromDomainPart = domainMatch.groupValues[1].substringBeforeLast(".")
        }

        var serviceNameFromSenderDisplayName = from.substringBefore('<').trim().removeSurrounding("\"")
        if (serviceNameFromSenderDisplayName.contains("@") || serviceNameFromSenderDisplayName.length > 30 || serviceNameFromSenderDisplayName.isEmpty() || serviceNameFromSenderDisplayName.length < 3) {
            serviceNameFromSenderDisplayName = ""
        }

        val subjectKeywords = subject.split(Regex("[^a-zA-Z0-9İıÖöÜüÇçŞşĞğ]+"))
            .filter { it.length in 4..20 && it.firstOrNull()?.isLetter() == true && it.first().isUpperCase() && !it.matches(Regex("^[A-ZİÖÜüÇŞĞ]+$")) }
            .distinct()

        val commonDomainsToAvoidAsName = listOf("google", "googlemail", "gmail", "facebook", "microsoft", "apple", "amazon", "yahoo", "outlook", "hotmail", "support", "info", "noreply", "service", "team", "mail", "email", "com", "newsletter", "update", "alert", "bildirim", "duyuru", "haber", "mailchimp", "sendgrid")

        if (serviceNameFromSenderDisplayName.isNotBlank() && !commonDomainsToAvoidAsName.any { serviceNameFromSenderDisplayName.lowercase().contains(it) }) {
            return serviceNameFromSenderDisplayName.capitalizeWords()
        }

        if (subjectKeywords.isNotEmpty()) {
            val bestSubjectKeyword = subjectKeywords.firstOrNull { keyword -> !commonDomainsToAvoidAsName.any { keyword.lowercase().contains(it) } }
            if (bestSubjectKeyword != null) {
                return bestSubjectKeyword.capitalizeWords()
            }
        }

        val fullDomain = domainMatch?.groupValues?.get(1)
        if (fullDomain != null) {
            val parts = fullDomain.split('.')
            if (parts.isNotEmpty()) {
                val potentialNameFromDomain = if (parts.size > 1 && commonDomainsToAvoidAsName.contains(parts.getOrNull(parts.size - 2)?.lowercase())) {
                    if (parts.size > 2 && !commonDomainsToAvoidAsName.contains(parts.getOrNull(parts.size - 3)?.lowercase())) parts.getOrNull(parts.size - 3) else parts.firstOrNull()
                } else {
                    parts.firstOrNull()
                }
                if (potentialNameFromDomain != null && potentialNameFromDomain.length > 2 && !commonDomainsToAvoidAsName.contains(potentialNameFromDomain.lowercase()))
                    return potentialNameFromDomain.capitalizeWords()
            }
        }
        return serviceNameFromDomainPart?.capitalizeWords()?.takeIf { it.isNotBlank() && !commonDomainsToAvoidAsName.contains(it.lowercase()) } ?: "Unknown Service"
    }

    private fun isPotentiallyReliableSenderForHeuristics(from: String): Boolean {
        val lowerFrom = from.lowercase()
        if (listOf("gmail.com", "outlook.com", "hotmail.com", "yahoo.com", "icloud.com", "yandex.com").any { lowerFrom.contains(it) }) {
            return listOf("no-reply", "noreply", "support", "billing", "account", "service", "team", "info", "do_not_reply", "customer").any { lowerFrom.substringBefore('@').contains(it) }
        }
        return true
    }

    /**
     * Turkish: YENİ İKİ AŞAMALI SİSTEM - E-postaları iki aşamada analiz eder.
     * English: NEW TWO-STAGE SYSTEM - Analyzes emails in two stages.
     */
    suspend fun classifyEmailsTwoStage(
        allRawEmails: List<RawEmail>,
        onProgress: ((progress: Float, status: String) -> Unit)? = null
    ): List<SubscriptionItem> = coroutineScope {
        Log.i("SubscriptionClassifier", "🚀 YENİ İKİ AŞAMALI SİSTEM BAŞLADI - ${allRawEmails.size} e-posta analiz edilecek")

        val totalEmails = allRawEmails.size
        val startTime = System.currentTimeMillis()

        onProgress?.invoke(0f, "E-postalar hazırlanıyor...")

        // AŞAMA 1: Tüm e-postaların başlık ve domain'ini analiz et
        Log.i("SubscriptionClassifier", "📧 AŞAMA 1: Başlık ve domain analizi başlıyor...")
        onProgress?.invoke(0.1f, "Aşama 1: E-posta başlıkları ve domain'leri analiz ediliyor...")

        val stage1Results = mutableListOf<TwoStageAnalysisResult>()
        val stage1Jobs = allRawEmails.mapIndexed { index, email ->
            async {
                try {
                    val domain = extractDomain(email.from) ?: "unknown"
                    val result = huggingFaceRepository.analyzeEmailForSubscriptionCompany(
                        emailIndex = index,
                        domain = domain,
                        subject = email.subject
                    )

                    synchronized(stage1Results) {
                        stage1Results.add(result)
                    }

                    // Progress güncelleme
                    val currentProgress = (stage1Results.size.toFloat() / totalEmails) * 0.4f + 0.1f // %10-50 arası
                    val elapsedTime = System.currentTimeMillis() - startTime
                    val avgTimePerEmail = if (stage1Results.size > 0) elapsedTime / stage1Results.size else 0
                    val remainingEmails = totalEmails - stage1Results.size
                    val estimatedRemainingTime = (remainingEmails * avgTimePerEmail) / 1000

                    val status = "Aşama 1: ${stage1Results.size}/${totalEmails} e-posta analiz edildi" +
                                if (estimatedRemainingTime > 0) " (Tahmini ${estimatedRemainingTime.toInt()}s kaldı)" else ""

                    onProgress?.invoke(currentProgress, status)

                } catch (e: Exception) {
                    Log.e("SubscriptionClassifier", "Aşama 1 hatası - E-posta $index: ${e.message}", e)
                }
            }
        }

        // Aşama 1'in tamamlanmasını bekle
        stage1Jobs.awaitAll()

        // Abonelik şirketi olarak tespit edilen e-postaları filtrele
        val subscriptionEmails = stage1Results.filter { it.isSubscriptionCompany && it.companyConfidence >= 0.6f }
        Log.i("SubscriptionClassifier", "✅ AŞAMA 1 TAMAMLANDI: ${subscriptionEmails.size} abonelik e-postası tespit edildi")

        if (subscriptionEmails.isEmpty()) {
            onProgress?.invoke(1.0f, "Tamamlandı! Hiç abonelik e-postası bulunamadı.")
            return@coroutineScope emptyList()
        }

        // AŞAMA 2: Tespit edilen e-postaları eskiden yeniye sıralayıp türlerine göre sınıflandır
        Log.i("SubscriptionClassifier", "📊 AŞAMA 2: E-posta türü sınıflandırması başlıyor...")
        onProgress?.invoke(0.5f, "Aşama 2: E-posta türleri sınıflandırılıyor...")

        // E-postaları eskiden yeniye sırala
        val sortedSubscriptionEmails = subscriptionEmails.map { result ->
            val email = allRawEmails[result.emailIndex]
            result.copy(rawEmailContent = prepareEmailContentForClassification(email))
        }.sortedBy { allRawEmails[it.emailIndex].date } // En eski e-posta ilk

        val finalResults = mutableMapOf<String, FinalSubscriptionStatus>()
        val processedCount = java.util.concurrent.atomic.AtomicInteger(0)

        val stage2Jobs = sortedSubscriptionEmails.map { result ->
            async {
                try {
                    val email = allRawEmails[result.emailIndex]
                    val emailContent = result.rawEmailContent ?: prepareEmailContentForClassification(email)

                    val (emailType, confidence) = huggingFaceRepository.classifySubscriptionEmailType(
                        emailContent = emailContent,
                        companyName = result.companyName
                    )

                    // Sonuçları güncelle (aynı şirketten gelen yeni e-postalar önceki kayıtları günceller)
                    synchronized(finalResults) {
                        val normalizedCompanyName = normalizeCompanyName(result.companyName)

                        if (emailType != "none" && confidence >= 0.6f) {
                            finalResults[normalizedCompanyName] = FinalSubscriptionStatus(
                                companyName = normalizedCompanyName,
                                status = emailType,
                                lastEmailDate = email.date,
                                emailCount = (finalResults[normalizedCompanyName]?.emailCount ?: 0) + 1,
                                confidence = confidence
                            )

                            Log.d("SubscriptionClassifier", "✅ $normalizedCompanyName: $emailType (güven: ${String.format("%.2f", confidence)})")
                        }
                    }

                    // Progress güncelleme
                    val currentProcessed = processedCount.incrementAndGet()
                    val progress = 0.5f + (currentProcessed.toFloat() / sortedSubscriptionEmails.size) * 0.4f // %50-90 arası
                    val status = "Aşama 2: ${currentProcessed}/${sortedSubscriptionEmails.size} e-posta sınıflandırıldı"

                    onProgress?.invoke(progress, status)

                } catch (e: Exception) {
                    Log.e("SubscriptionClassifier", "Aşama 2 hatası: ${e.message}", e)
                    processedCount.incrementAndGet()
                }
            }
        }

        // Aşama 2'nin tamamlanmasını bekle
        stage2Jobs.awaitAll()

        // Final sonuçları SubscriptionItem'lara dönüştür
        onProgress?.invoke(0.95f, "Sonuçlar hazırlanıyor...")

        val subscriptionItems = finalResults.values.map { status ->
            createSubscriptionItemFromFinalStatus(status, allRawEmails)
        }

        Log.i("SubscriptionClassifier", "🎯 İKİ AŞAMALI SİSTEM TAMAMLANDI: ${subscriptionItems.size} abonelik oluşturuldu")
        subscriptionItems.forEach { subscription ->
            Log.i("SubscriptionClassifier", "✅ ${subscription.serviceName}: ${subscription.status.name} (${subscription.emailCount} e-posta)")
        }

        onProgress?.invoke(1.0f, "Tamamlandı! ${subscriptionItems.size} abonelik bulundu.")

        return@coroutineScope subscriptionItems
    }

    /**
     * Turkish: FinalSubscriptionStatus'tan SubscriptionItem oluşturur.
     * English: Creates SubscriptionItem from FinalSubscriptionStatus.
     */
    private fun createSubscriptionItemFromFinalStatus(
        status: FinalSubscriptionStatus,
        allEmails: List<RawEmail>
    ): SubscriptionItem {
        val subscriptionStatus = when (status.status) {
            "subscription_start" -> SubscriptionStatus.ACTIVE
            "subscription_cancel" -> SubscriptionStatus.CANCELLED
            else -> SubscriptionStatus.UNKNOWN
        }

        // Bu şirketten gelen e-postaları bul
        val companyEmails = allEmails.filter { email ->
            val domain = extractDomain(email.from) ?: ""
            normalizeCompanyName(extractGeneralServiceName(email.from, email.subject, email.bodySnippet)) == status.companyName
        }

        return SubscriptionItem(
            serviceName = status.companyName.capitalizeWords(),
            status = subscriptionStatus,
            emailCount = status.emailCount,
            lastEmailDate = status.lastEmailDate,
            cancellationDate = if (subscriptionStatus == SubscriptionStatus.CANCELLED) status.lastEmailDate else null,
            relatedEmailIds = companyEmails.map { it.id },
            subscriptionStartDate = companyEmails.minByOrNull { it.date }?.date
        )
    }
}

fun String.capitalizeWords(): String = this.split(Regex("\\s+")).joinToString(" ") { word ->
    if (word.isEmpty()) {
        ""
    } else {
        word.replaceFirstChar { char ->
            if (char.isLowerCase()) {
                char.titlecase()
            } else {
                char.toString()
            }
        }
    }
}