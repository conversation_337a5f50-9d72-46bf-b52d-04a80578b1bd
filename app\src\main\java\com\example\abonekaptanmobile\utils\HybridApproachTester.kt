// file: app/java/com/example/abonekaptanmobile/utils/HybridApproachTester.kt
package com.example.abonekaptanmobile.utils

import android.util.Log
import com.example.abonekaptanmobile.data.repository.HuggingFaceRepository
import com.example.abonekaptanmobile.model.RawEmail
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

data class TestResult(
    val emailFrom: String,
    val emailSubject: String,
    val domain: String,
    val companyName: String,
    val emailType: String,
    val isSubscriptionRelated: Boolean,
    val isPaidSubscription: Boolean,
    val overallConfidence: Float,
    val isReliable: Boolean,
    val error: String? = null
)

/**
 * Turkish: Hybrid Approach sistemini test etmek için yardımcı sınıf.
 * English: Helper class for testing the Hybrid Approach system.
 */
@Singleton
class HybridApproachTester @Inject constructor(
    private val huggingFaceRepository: HuggingFaceRepository
) {
    companion object {
        private const val TAG = "HybridApproachTester"
    }

    private val _testResults = MutableStateFlow<List<TestResult>>(emptyList())
    val testResults: StateFlow<List<TestResult>> = _testResults.asStateFlow()

    private val _isTestingInProgress = MutableStateFlow(false)
    val isTestingInProgress: StateFlow<Boolean> = _isTestingInProgress.asStateFlow()

    /**
     * Turkish: Test email'leri ile Hybrid Approach'u test eder.
     * English: Tests Hybrid Approach with test emails.
     */
    fun testHybridApproach() {
        CoroutineScope(Dispatchers.IO).launch {
            _isTestingInProgress.value = true
            _testResults.value = emptyList()

            Log.i(TAG, "=== HYBRID APPROACH TEST BAŞLADI ===")

            val testEmails = createTestEmails()
            val results = mutableListOf<TestResult>()

            testEmails.forEachIndexed { index, email ->
                Log.i(TAG, "\n--- Test Email ${index + 1} ---")
                Log.i(TAG, "From: ${email.from}")
                Log.i(TAG, "Subject: ${email.subject}")

                try {
                    // Email domain'ini çıkar
                    val domain = extractDomain(email.from) ?: "unknown"
                    val emailContent = prepareEmailContent(email)

                    // YEREL HYBRID APPROACH - 3 Adım (HuggingFace API devre dışı)
                    Log.i(TAG, "Adım 1: Yerel domain analizi...")
                    val companyResult = createLocalCompanyResult(domain)

                    Log.i(TAG, "Adım 2: Yerel email türü tespiti...")
                    val emailTypeResult = createLocalDetailedEmailTypeResult(emailContent, companyResult.label)

                    Log.i(TAG, "Adım 3: Yerel doğrulama...")
                    val validation = createLocalValidation(companyResult, emailTypeResult)

                    // Sonuçları logla
                    Log.i(TAG, "SONUÇ: ${validation.getSummary()}")
                    Log.i(TAG, "Abonelik İlgili: ${validation.isSubscriptionRelated()}")
                    Log.i(TAG, "Ücretli Abonelik: ${validation.isPaidSubscription()}")

                    // Test sonucunu oluştur
                    val testResult = TestResult(
                        emailFrom = email.from,
                        emailSubject = email.subject,
                        domain = domain,
                        companyName = validation.companyName,
                        emailType = validation.emailType,
                        isSubscriptionRelated = validation.isSubscriptionRelated(),
                        isPaidSubscription = validation.isPaidSubscription(),
                        overallConfidence = validation.overallConfidence,
                        isReliable = validation.isReliable
                    )
                    results.add(testResult)

                } catch (e: Exception) {
                    Log.e(TAG, "Test email ${index + 1} için hata: ${e.message}", e)

                    // Hata durumunda da sonuç ekle
                    val errorResult = TestResult(
                        emailFrom = email.from,
                        emailSubject = email.subject,
                        domain = extractDomain(email.from) ?: "unknown",
                        companyName = "ERROR",
                        emailType = "ERROR",
                        isSubscriptionRelated = false,
                        isPaidSubscription = false,
                        overallConfidence = 0f,
                        isReliable = false,
                        error = e.message
                    )
                    results.add(errorResult)
                }

                // Sonuçları güncelle (her email sonrası)
                _testResults.value = results.toList()
            }

            Log.i(TAG, "=== HYBRID APPROACH TEST BİTTİ ===")
            _isTestingInProgress.value = false
        }
    }

    /**
     * Turkish: Test için örnek email'ler oluşturur.
     * English: Creates sample emails for testing.
     */
    private fun createTestEmails(): List<RawEmail> {
        return listOf(
            // Netflix abonelik başlatma
            RawEmail(
                id = "test1",
                threadId = "thread1",
                from = "<EMAIL>",
                to = listOf("<EMAIL>"),
                subject = "Welcome to Netflix! Your subscription is now active",
                snippet = "Welcome to Netflix subscription",
                bodyPlainText = "Welcome to Netflix! Your subscription is now active. Enjoy unlimited streaming.",
                bodyHtml = "<p>Welcome to Netflix! Your subscription is now active. Enjoy unlimited streaming.</p>",
                date = System.currentTimeMillis(),
                bodySnippet = "Thank you for subscribing to Netflix. Your monthly subscription has started."
            ),

            // Spotify ödeme onayı
            RawEmail(
                id = "test2",
                threadId = "thread2",
                from = "<EMAIL>",
                to = listOf("<EMAIL>"),
                subject = "Payment confirmation - Spotify Premium",
                snippet = "Payment confirmation Spotify",
                bodyPlainText = "Payment confirmation for your Spotify Premium subscription.",
                bodyHtml = "<p>Your payment of $9.99 for Spotify Premium has been processed successfully.</p>",
                date = System.currentTimeMillis(),
                bodySnippet = "Your payment of $9.99 for Spotify Premium has been processed successfully."
            ),

            // Amazon Prime iptal
            RawEmail(
                id = "test3",
                threadId = "thread3",
                from = "<EMAIL>",
                to = listOf("<EMAIL>"),
                subject = "Your Amazon Prime membership has been cancelled",
                snippet = "Amazon Prime cancelled",
                bodyPlainText = "Your Amazon Prime membership cancellation is confirmed.",
                bodyHtml = "<p>We've cancelled your Amazon Prime membership as requested.</p>",
                date = System.currentTimeMillis(),
                bodySnippet = "We've cancelled your Amazon Prime membership as requested."
            ),

            // Disney+ promosyon
            RawEmail(
                id = "test4",
                threadId = "thread4",
                from = "<EMAIL>",
                to = listOf("<EMAIL>"),
                subject = "Special offer: Get Disney+ for just $5.99/month",
                snippet = "Disney+ special offer",
                bodyPlainText = "Special promotional offer for Disney+ subscription.",
                bodyHtml = "<p>Limited time offer! Subscribe to Disney+ now and save 25%.</p>",
                date = System.currentTimeMillis(),
                bodySnippet = "Limited time offer! Subscribe to Disney+ now and save 25%."
            ),

            // Bilinmeyen şirket
            RawEmail(
                id = "test5",
                threadId = "thread5",
                from = "<EMAIL>",
                to = listOf("<EMAIL>"),
                subject = "Your subscription renewal",
                snippet = "Subscription renewal",
                bodyPlainText = "Your subscription has been renewed for another month.",
                bodyHtml = "<p>Your monthly subscription has been renewed automatically.</p>",
                date = System.currentTimeMillis(),
                bodySnippet = "Your monthly subscription has been renewed automatically."
            )
        )
    }

    /**
     * Turkish: Email domain'ini çıkarır.
     * English: Extracts email domain.
     */
    private fun extractDomain(emailAddress: String): String? {
        val domainMatch = Regex("@([a-zA-Z0-9.-]+)").find(emailAddress)
        return domainMatch?.groupValues?.get(1)
    }

    /**
     * Ücretli abonelik şirketleri listesi - SubscriptionClassifier ile aynı
     */
    private val paidSubscriptionCompanies = setOf(
        // Video Streaming
        "netflix", "amazon", "disney", "disneyplus", "hbo", "hbomax", "max", "hulu", "peacock",
        "paramount", "crunchyroll", "starz", "espn", "dazn", "mubi", "jiocinema", "globoplay",
        "viu", "discovery", "zee5", "rtl", "ivi", "shahid", "youtube", "exxen", "blutv",
        "gain", "dsmart", "digiturk", "tivibu", "tod",

        // Music Streaming
        "spotify", "apple", "tidal", "deezer", "soundcloud",

        // Books & Audio
        "audible", "kindle", "storytel", "medium",

        // Education
        "coursera", "udemy", "linkedin", "masterclass", "codecademy", "udacity", "skillshare",
        "duolingo", "babbel", "rosetta", "brilliant", "photomath", "pluralsight",

        // Health & Fitness
        "peloton", "classpass", "fitbit", "calm", "headspace", "noom", "tonal", "mirror",
        "zwift", "betterhelp", "talkspace", "nike", "strava",

        // News & Finance
        "bloomberg", "wsj", "ft", "economist", "nytimes", "washingtonpost", "forbes",
        "barrons", "yahoo", "seeking", "morningstar", "reuters",

        // Gaming
        "xbox", "playstation", "nintendo", "ea", "ubisoft", "twitch", "discord", "roblox",

        // Software & SaaS
        "microsoft", "google", "adobe", "salesforce", "aws", "azure", "zoom", "slack",
        "atlassian", "dropbox", "box", "github", "gitlab", "canva", "asana", "trello",
        "docusign", "eventbrite", "grammarly", "webflow", "hubspot", "mailchimp",
        "surveymonkey", "zendesk", "servicenow", "twilio", "autodesk", "shopify",

        // Retail & Subscription Boxes
        "prime", "costco", "sams", "hellofresh", "blueapron", "dollarshave", "birchbox",
        "barkbox", "graze",

        // Content Platforms
        "patreon", "onlyfans"
    )

    /**
     * Yerel şirket sınıflandırması - sadece ücretli abonelik şirketlerini tespit eder
     */
    private fun createLocalCompanyResult(domain: String): com.example.abonekaptanmobile.data.remote.model.ClassificationResult {
        val domainLower = domain.lowercase()

        // Önce domain'de bilinen şirket var mı kontrol et
        val detectedCompany = paidSubscriptionCompanies.find { company ->
            domainLower.contains(company) ||
            domainLower.contains("$company.") ||
            domainLower.startsWith(company) ||
            domainLower.endsWith(company)
        }

        return if (detectedCompany != null) {
            // Bilinen ücretli abonelik şirketi
            val confidence = when (detectedCompany) {
                in listOf("netflix", "spotify", "disney", "adobe", "microsoft") -> 0.98f
                in listOf("youtube", "apple", "amazon") -> 0.95f
                else -> 0.90f
            }

            com.example.abonekaptanmobile.data.remote.model.ClassificationResult(
                label = detectedCompany,
                score = confidence
            )
        } else {
            // Bilinmeyen şirket - abonelik değil
            com.example.abonekaptanmobile.data.remote.model.ClassificationResult(
                label = "unknown",
                score = 0.1f // Çok düşük confidence
            )
        }
    }

    /**
     * Yerel detaylı email türü sınıflandırması - gelişmiş pattern matching
     */
    private fun createLocalDetailedEmailTypeResult(emailContent: String, companyName: String): com.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult {
        val content = emailContent.lowercase()
        val subject = emailContent.take(200).lowercase()

        val patterns = mapOf(
            "subscription_start" to listOf("welcome", "başladı", "activated", "subscription.*start", "üyelik.*başla", "hoş geldin", "welcome to", "subscription.*active", "plan.*active"),
            "subscription_cancel" to listOf("cancel", "iptal", "unsubscribe", "terminated", "ended", "sona erdi", "subscription.*cancel", "üyelik.*iptal", "abonelik.*iptal"),
            "payment_confirmation" to listOf("payment.*confirm", "ödeme.*onay", "receipt", "invoice", "fatura", "makbuz", "payment.*successful", "ödeme.*başarılı", "charged"),
            "trial_ending" to listOf("trial.*end", "deneme.*bitiyor", "trial.*expir", "free.*trial.*end", "ücretsiz.*deneme.*bitiyor", "trial.*sona"),
            "subscription_renewal" to listOf("renewal", "yenileme", "renew", "subscription.*renew", "abonelik.*yenile", "auto.*renew", "otomatik.*yenileme"),
            "plan_change" to listOf("upgrade", "downgrade", "plan.*change", "plan.*değiş", "yükselt", "plan.*upgrade", "subscription.*change"),
            "promotional" to listOf("offer", "discount", "sale", "promo", "kampanya", "indirim", "special.*offer", "özel.*teklif", "limited.*time")
        )

        val scores = mutableMapOf<String, Float>()

        for ((emailType, patternList) in patterns) {
            var score = 0f
            var matchCount = 0

            for (pattern in patternList) {
                val regex = Regex(pattern, RegexOption.IGNORE_CASE)
                if (regex.containsMatchIn(content)) {
                    matchCount++
                    score += if (regex.containsMatchIn(subject)) 0.3f else 0.2f
                }
            }

            if (companyName != "unknown" && companyName != "other") {
                score += 0.1f
            }

            if (matchCount > 1) {
                score += matchCount * 0.1f
            }

            scores[emailType] = minOf(score, 1.0f)
        }

        val primaryType = scores.maxByOrNull { it.value }
        val primaryLabel = primaryType?.key ?: "subscription_related"
        val primaryScore = primaryType?.value ?: 0.5f

        val allResults = scores.map { (type, score) ->
            com.example.abonekaptanmobile.data.remote.model.ClassificationResult(label = type, score = score)
        }.sortedByDescending { it.score }

        return com.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult(
            primaryLabel = primaryLabel,
            primaryScore = primaryScore,
            allResults = allResults
        )
    }

    /**
     * Yerel doğrulama sistemi - gelişmiş güvenilirlik kontrolü
     */
    private fun createLocalValidation(
        companyResult: com.example.abonekaptanmobile.data.remote.model.ClassificationResult,
        emailTypeResult: com.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult
    ): com.example.abonekaptanmobile.data.remote.model.HybridValidationResult {
        val companyWeight = 0.6f
        val emailTypeWeight = 0.4f

        val overallConfidence = (companyResult.score * companyWeight) + (emailTypeResult.primaryScore * emailTypeWeight)

        val isReliable = when {
            overallConfidence > 0.9f -> true
            overallConfidence > 0.7f && companyResult.label != "unknown" -> true
            overallConfidence < 0.5f -> false
            companyResult.label == "unknown" && emailTypeResult.primaryScore < 0.6f -> false
            else -> overallConfidence > 0.75f
        }

        return com.example.abonekaptanmobile.data.remote.model.HybridValidationResult(
            companyName = companyResult.label,
            companyConfidence = companyResult.score,
            emailType = emailTypeResult.primaryLabel,
            emailTypeConfidence = emailTypeResult.primaryScore,
            overallConfidence = overallConfidence,
            isReliable = isReliable,
            detailedEmailTypeResult = emailTypeResult
        )
    }

    /**
     * HuggingFace API hatası durumunda fallback email türü sonucu oluşturur
     */
    private fun createFallbackEmailTypeResult(emailContent: String): com.example.abonekaptanmobile.data.remote.model.ClassificationResult {
        val content = emailContent.lowercase()

        val emailType = when {
            content.contains("subscription") && (content.contains("start") || content.contains("welcome") || content.contains("activated")) -> "subscription_start"
            content.contains("cancel") || content.contains("unsubscribe") || content.contains("terminated") -> "subscription_cancel"
            content.contains("payment") && (content.contains("confirm") || content.contains("receipt") || content.contains("invoice")) -> "payment_confirmation"
            content.contains("trial") && content.contains("end") -> "trial_ending"
            content.contains("renewal") || content.contains("renew") -> "subscription_renewal"
            content.contains("upgrade") || content.contains("plan") -> "plan_change"
            else -> "subscription_related"
        }

        return com.example.abonekaptanmobile.data.remote.model.ClassificationResult(
            label = emailType,
            score = 0.85f // Yüksek confidence çünkü kelime eşleştirmesi güvenilir
        )
    }

    /**
     * HuggingFace API hatası durumunda fallback detaylı email türü sonucu oluşturur
     */
    private fun createFallbackDetailedEmailTypeResult(emailContent: String): com.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult {
        val primaryResult = createFallbackEmailTypeResult(emailContent)

        return com.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult(
            primaryLabel = primaryResult.label,
            primaryScore = primaryResult.score,
            allResults = listOf(primaryResult)
        )
    }

    /**
     * HuggingFace API hatası durumunda fallback doğrulama sonucu oluşturur
     */
    private fun createFallbackValidation(
        companyResult: com.example.abonekaptanmobile.data.remote.model.ClassificationResult,
        emailTypeResult: com.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult
    ): com.example.abonekaptanmobile.data.remote.model.HybridValidationResult {
        val overallConfidence = (companyResult.score + emailTypeResult.primaryScore) / 2
        val isReliable = overallConfidence > 0.8f

        return com.example.abonekaptanmobile.data.remote.model.HybridValidationResult(
            companyName = companyResult.label,
            companyConfidence = companyResult.score,
            emailType = emailTypeResult.primaryLabel,
            emailTypeConfidence = emailTypeResult.primaryScore,
            overallConfidence = overallConfidence,
            isReliable = isReliable,
            detailedEmailTypeResult = emailTypeResult
        )
    }

    /**
     * Turkish: Email içeriğini hazırlar.
     * English: Prepares email content.
     */
    private fun prepareEmailContent(email: RawEmail): String {
        val bodyContent = when {
            email.bodySnippet != null -> email.bodySnippet
            email.snippet != null -> email.snippet
            else -> email.bodyPlainText.take(500)
        }
        return "Subject: ${email.subject}\nFrom: ${email.from}\nContent: $bodyContent"
    }
}
