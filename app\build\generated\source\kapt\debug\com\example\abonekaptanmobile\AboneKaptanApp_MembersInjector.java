// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile;

import androidx.hilt.work.HiltWorkerFactory;
import com.example.abonekaptanmobile.data.repository.CommunityPatternRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AboneKaptanApp_MembersInjector implements MembersInjector<AboneKaptanApp> {
  private final Provider<HiltWorkerFactory> workerFactoryProvider;

  private final Provider<CommunityPatternRepository> communityPatternRepositoryProvider;

  public AboneKaptanApp_MembersInjector(Provider<HiltWorkerFactory> workerFactoryProvider,
      Provider<CommunityPatternRepository> communityPatternRepositoryProvider) {
    this.workerFactoryProvider = workerFactoryProvider;
    this.communityPatternRepositoryProvider = communityPatternRepositoryProvider;
  }

  public static MembersInjector<AboneKaptanApp> create(
      Provider<HiltWorkerFactory> workerFactoryProvider,
      Provider<CommunityPatternRepository> communityPatternRepositoryProvider) {
    return new AboneKaptanApp_MembersInjector(workerFactoryProvider, communityPatternRepositoryProvider);
  }

  @Override
  public void injectMembers(AboneKaptanApp instance) {
    injectWorkerFactory(instance, workerFactoryProvider.get());
    injectCommunityPatternRepository(instance, communityPatternRepositoryProvider.get());
  }

  @InjectedFieldSignature("com.example.abonekaptanmobile.AboneKaptanApp.workerFactory")
  public static void injectWorkerFactory(AboneKaptanApp instance, HiltWorkerFactory workerFactory) {
    instance.workerFactory = workerFactory;
  }

  @InjectedFieldSignature("com.example.abonekaptanmobile.AboneKaptanApp.communityPatternRepository")
  public static void injectCommunityPatternRepository(AboneKaptanApp instance,
      CommunityPatternRepository communityPatternRepository) {
    instance.communityPatternRepository = communityPatternRepository;
  }
}
